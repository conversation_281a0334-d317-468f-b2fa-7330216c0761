﻿using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using Refit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Zendar.Business.API.ZendarSync.Clients;
using Zendar.Business.API.ZendarSync.Dtos.Link.Request;
using Zendar.Business.API.ZendarSync.Handlers;
using Zendar.Business.API.ZendarSync.Services;
using Zendar.Business.Consts;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.Fiscal;
using Zendar.Business.Services.CorrecaoServices.ProdutoCorrecaoServices;
using Zendar.Business.Services.IntegracaoServices.IntegracaoService;
using Zendar.Business.Services.V2.ProdutoV2Services.Facade;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorImagemService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorTamanhoServices.ProdutoCorTamanhoService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoService;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Integracao.IntegracaoSnapshot;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Business.ViewModels.V2.TabelaPrecoViewModels;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services.IntegracaoServices.ProdutoPlataformaService
{
    public class ProdutoPlataformaService : IProdutoPlataformaService
    {
        #region Properties

        private readonly INotificador _notificador;
        private readonly IProdutoCorRepository _produtoCorRepository;
        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;
        private readonly IUnidadeMedidaService _unidadeMedidaService;
        private readonly IMarcaService _marcaService;
        private readonly ICategoriaProdutoService _categoriaProdutoService;
        private readonly ICorService _corService;
        private readonly ITamanhoService _tamanhoService;
        private readonly IProdutoV2Service _produtoV2Service;
        private readonly IProdutoCorV2Service _produtoCorV2Service;
        private readonly IProdutoCorTamanhoV2Service _produtoCorTamanhoV2Service;
        private readonly IProdutoCorImagemV2Service _produtoCorImagemV2Service;
        private readonly IProdutoV2Facade _produtoV2Facade;
        private readonly ILojaService _lojaService;
        private readonly ILojaFiscalService _lojaFiscalService;
        private readonly ITabelaPrecoService _tabelaPrecoService;
        private readonly IRegraFiscalService _regraFiscalService;
        private readonly IIntegracaoService _integracaoService;
        private readonly IProdutoCorrecaoService _produtoCorrecaoService;
		private readonly ILogErroService _logErroService;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly SyncJwtHandler _syncJwtHandler;
        private IZendarSyncEcommerceService<ITrayApi> _zendarSyncTrayApi;

		#endregion

		#region Construtor

		public ProdutoPlataformaService(
			INotificador notificador,
			IProdutoCorRepository produtoCorRepository,
			IProdutoCorTamanhoRepository produtoCorTamanhoRepository,
			IUnidadeMedidaService unidadeMedidaService,
			IMarcaService marcaService,
			ICategoriaProdutoService categoriaProdutoService,
			ICorService corService,
			ITamanhoService tamanhoService,
			IProdutoV2Service produtoV2Service,
			IProdutoCorV2Service produtoCorV2Service,
			IProdutoCorTamanhoV2Service produtoCorTamanhoV2Service,
			IProdutoCorImagemV2Service produtoCorImagemV2Service,
			IProdutoV2Facade produtoV2Facade,
			ILojaService lojaService,
			ILojaFiscalService lojaFiscalService,
			ITabelaPrecoService tabelaPrecoService,
			IRegraFiscalService regraFiscalService,
			IIntegracaoService integracaoService,
			ILogErroService logErroService,
			IAspNetUserInfo aspNetUserInfo,
			SyncJwtHandler syncJwtHandler,
			IProdutoCorrecaoService produtoCorrecaoService)
		{
			_notificador = notificador;
			_produtoCorRepository = produtoCorRepository;
			_produtoCorTamanhoRepository = produtoCorTamanhoRepository;
			_unidadeMedidaService = unidadeMedidaService;
			_marcaService = marcaService;
			_categoriaProdutoService = categoriaProdutoService;
			_corService = corService;
			_tamanhoService = tamanhoService;
			_produtoV2Service = produtoV2Service;
			_produtoCorV2Service = produtoCorV2Service;
			_produtoCorTamanhoV2Service = produtoCorTamanhoV2Service;
			_produtoCorImagemV2Service = produtoCorImagemV2Service;
			_produtoV2Facade = produtoV2Facade;
			_lojaService = lojaService;
			_lojaFiscalService = lojaFiscalService;
			_tabelaPrecoService = tabelaPrecoService;
			_regraFiscalService = regraFiscalService;
			_integracaoService = integracaoService;
			_logErroService = logErroService;
			_aspNetUserInfo = aspNetUserInfo;
			_syncJwtHandler = syncJwtHandler;
			_produtoCorrecaoService = produtoCorrecaoService;
		}

		#endregion

		#region Public Methods

		public async Task<ProdutoV2ViewModel> CadastrarAlterarProduto(
            ProdutoSnapshotViewModel produtoSnapshotViewModel,
            int identificacaoIntegracao,
            Guid lojaId,
            Guid? tabelaPrecoId,
            Guid tabelaPrecoPadraoId,
            string url,
			bool withTransaction = true)
        {
            _notificador.Limpar();

            PrepararConfiguracao(identificacaoIntegracao, url);

            var produtoV2ViewModel = new ProdutoV2ViewModel();
            produtoV2ViewModel.DataHoraCadastro = produtoSnapshotViewModel.DataHoraCadastro;
            produtoV2ViewModel.DataHoraUltimaAlteracao = produtoSnapshotViewModel.DataHoraUltimaAlteracao;
            produtoV2ViewModel.Ativo = produtoSnapshotViewModel.Anunciado;

            #region Dados Gerais

            produtoV2ViewModel.TipoProduto = (TipoProduto)produtoSnapshotViewModel.TipoProduto;
            produtoV2ViewModel.Nome = produtoSnapshotViewModel.Nome;

            if (!string.IsNullOrEmpty(produtoSnapshotViewModel.NomeAbreviado))
                produtoV2ViewModel.NomeAbreviado = produtoSnapshotViewModel.NomeAbreviado.Length > 50 ? null : produtoSnapshotViewModel.NomeAbreviado;

            produtoV2ViewModel.Referencia = produtoSnapshotViewModel.Referencia;
            produtoV2ViewModel.VideoUrl = produtoSnapshotViewModel.VideoUrl;
            produtoV2ViewModel.VenderEcommerce = true;

            #region Marca

            if (produtoSnapshotViewModel.Marca != null)
            {
                var marcaId =
                    await CadastrarAlterarMarca(produtoSnapshotViewModel.Marca,
                                                identificacaoIntegracao,
                                                url);

                produtoV2ViewModel.MarcaId = marcaId.Value;
            }
            else
            {
                var marcaGeral = new EntidadeSnapshotViewModel();
                marcaGeral.Nome = "GERAL";

                var marcaId =
                   await CadastrarAlterarMarca(marcaGeral,
                                               identificacaoIntegracao,
                                               url);

                produtoV2ViewModel.MarcaId = marcaId.Value;
            }

            #endregion

            #region CategoriaProduto

            if (produtoSnapshotViewModel.ListaCategoria != null)
            {
                var categoriaProdutoId =
                    await CadastrarAlterarListaCategoria(produtoSnapshotViewModel.ListaCategoria,
                                                         identificacaoIntegracao,
                                                         url);

                if (categoriaProdutoId != null)
                    produtoV2ViewModel.CategoriaProdutoId = categoriaProdutoId.Value;
            }

            #endregion

            #region UnidadeMedida

            var siglaUnidadeMedida = SystemConst.DEFAULT_UNIDADE_MEDIDA;

            var unidadeMedida =
                await _unidadeMedidaService.ObterComPredicate(c => c.Sigla == siglaUnidadeMedida);

            if (unidadeMedida != null)
                produtoV2ViewModel.UnidadeMedidaId = unidadeMedida.Id;

            #endregion

            #endregion

            #region Tags

            produtoV2ViewModel.Tags = new Guid[0];

            #endregion

            #region Informacoes Adicionais

            produtoV2ViewModel.InformacoesAdicionais = new ProdutoInformacoesAdicionaisV2ViewModel();
            produtoV2ViewModel.InformacoesFood = new ProdutoInformacoesFoodV2ViewModel();

            #endregion

            #region Informacoes Fiscais

            ProdutoFiscaisV2ViewModel produtoFiscaisV2ViewModel = new ProdutoFiscaisV2ViewModel();
            produtoFiscaisV2ViewModel.TipoProdutoFiscal = ZendarPackage.NotaFiscal.Enums.TipoProdutoFiscal.Mercadoria_para_revenda;
            produtoFiscaisV2ViewModel.CstOrigem = ZendarPackage.NotaFiscal.Enums.OrigemMercadoria.NACIONAL_EXCETO_CODIGOS_3_4_5_8;

            var codigoNCM = SystemConst.DEFAULT_NCM;

            if (!string.IsNullOrEmpty(produtoSnapshotViewModel.CodigoNcm))
                produtoFiscaisV2ViewModel.CodigoNcm = produtoSnapshotViewModel.CodigoNcm;
            else
                produtoFiscaisV2ViewModel.CodigoNcm = codigoNCM;

            var lojaFiscal =
                await _lojaFiscalService.ObterComPredicate(c => c.LojaId == lojaId);

            if (lojaFiscal != null)
            {
                var regraFiscal =
                    await _regraFiscalService.ObterComPredicate(c => c.Id == lojaFiscal.RegraFiscalPadraoId);

                if (regraFiscal != null)
                    produtoFiscaisV2ViewModel.RegraFiscalId = regraFiscal.Id;
            }

            produtoFiscaisV2ViewModel.UnidadeTributavelId = produtoV2ViewModel.UnidadeMedidaId;
            produtoFiscaisV2ViewModel.FatorConversao = "*";
            produtoFiscaisV2ViewModel.QtdeConversao = 1;
            produtoFiscaisV2ViewModel.IndicadorEscalaRelevante = ZendarPackage.NotaFiscal.Enums.IndicadorEscalaRelevante.NENHUM;

            produtoV2ViewModel.InformacoesFiscais = produtoFiscaisV2ViewModel;

            #endregion

            #region Informacoes Variacao Padrao

            var informacoesVariacaoPadrao = new ProdutoVariacaoPadraoV2ViewModel();
            informacoesVariacaoPadrao.CodigoGTINEAN = produtoSnapshotViewModel.CodigoGTINEAN;
            informacoesVariacaoPadrao.OutrasInformacoesAltura = produtoSnapshotViewModel.Altura;
            informacoesVariacaoPadrao.OutrasInformacoesLargura = produtoSnapshotViewModel.Largura;
            informacoesVariacaoPadrao.OutrasInformacoesProfundidade = produtoSnapshotViewModel.Profundidade;
            informacoesVariacaoPadrao.OutrasInformacoesPesoLiquido = produtoSnapshotViewModel.PesoLiquido;
            informacoesVariacaoPadrao.OutrasInformacoesPesoBruto = produtoSnapshotViewModel.PesoBruto;
            informacoesVariacaoPadrao.OutrasInformacoesPesoEmbalagem = produtoSnapshotViewModel.PesoEmbalagem;

            if (produtoSnapshotViewModel.TipoProduto == (int)TipoProduto.PRODUTO_SIMPLES)
            {
                informacoesVariacaoPadrao.EstoqueMinimo = produtoSnapshotViewModel.EstoqueMinimo;
                informacoesVariacaoPadrao.EstoqueAtual = produtoSnapshotViewModel.EstoqueAtual;

                var listaProdutoCorImagemV2ViewModel = await PrepararListaProdutoCorImagem(produtoSnapshotViewModel.ListaImagem);

                if (listaProdutoCorImagemV2ViewModel != null &&
                    listaProdutoCorImagemV2ViewModel.Count > 0)
                    informacoesVariacaoPadrao.ProdutoCorImagens = listaProdutoCorImagemV2ViewModel.ToArray();
            }

            produtoV2ViewModel.InformacoesVariacaoPadrao = informacoesVariacaoPadrao;

            #endregion

            #region Precos

            produtoV2ViewModel.Precos = new ProdutoPrecosV2ViewModel();

            #region Produto Preco Loja

            var listaProdutoPrecoLoja =
                await PrepararListaProdutoPrecoLoja(produtoSnapshotViewModel.PrecoCusto,
                                                    produtoSnapshotViewModel.PrecoVenda);

            if (listaProdutoPrecoLoja != null &&
                listaProdutoPrecoLoja.Count > 0)
                produtoV2ViewModel.Precos.ProdutoPrecoLojas = listaProdutoPrecoLoja?.ToArray();

            #endregion

            #region Tabela Preco Produto
            
			var listaTabelaPrecoProduto =
			    await PrepararListaTabelaPrecoProduto(produtoSnapshotViewModel.PrecoCusto,
													produtoSnapshotViewModel.PrecoVenda,
													produtoSnapshotViewModel.ZendarId,
													tabelaPrecoId,
                                                    tabelaPrecoPadraoId);

			if (listaTabelaPrecoProduto != null &&
				listaTabelaPrecoProduto.Count > 0)
				produtoV2ViewModel.Precos.TabelaPrecoProdutos = listaTabelaPrecoProduto?.ToArray();

            #endregion

            #endregion

            #region Produto Cores

            if (produtoSnapshotViewModel.TipoProduto == (int)TipoProduto.PRODUTO_VARIACAO &&
                produtoSnapshotViewModel.ListaVariacao.Count > 0)
            {
                var listaProdutoCor =
                    await PrepararListaProdutoVariacao(produtoSnapshotViewModel,
                                                       identificacaoIntegracao,
                                                       tabelaPrecoId,
                                                       url);

                if (listaProdutoCor != null &&
                    listaProdutoCor.Count > 0)
                    produtoV2ViewModel.ProdutoCores = listaProdutoCor?.ToArray();
            }

            #endregion

            #region Produto Ecommerces

            var listaProdutoEcommerce =
                await PrepararListaProdutoEcommerce(produtoSnapshotViewModel,
                                                    identificacaoIntegracao);

            if (listaProdutoEcommerce != null &&
                listaProdutoEcommerce.Count > 0)
                produtoV2ViewModel.ProdutoEcommerces = listaProdutoEcommerce?.ToArray();

            #endregion

            #region Campos Personalizados

            produtoV2ViewModel.CamposPersonalizados = new CampoPersonalizadoValorViewModel[0];

            #endregion

            #region Vinculação

            Guid? produtoId = null;

            try
            {
                Produto produtoExistente = null;

                if (identificacaoIntegracao == (int)IdentificacaoIntegracao.TRAY)
                    produtoExistente = await _produtoV2Service.ObterComPredicate(c => c.ProdutoEcommerces.Any(c => c.IdentificacaoIntegracao == IdentificacaoIntegracao.TRAY && c.Referencia == produtoSnapshotViewModel.SiteId));

                if (produtoExistente == null)
                    produtoId =
                        await _produtoV2Facade.CadastrarCompleto(produtoV2ViewModel, false, withTransaction);
                else
                {
                    var tamanhosProduto = produtoV2ViewModel?.ProdutoCores?
                            .SelectMany(x => x.ProdutoCorTamanhos?
                            .Select(y => y.TamanhoId))
                            .Distinct();

                    if (produtoV2ViewModel.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
                        !tamanhosProduto.Any())
                    {
                        var listaTamanhoPadrao = new List<Guid>();

                        var tamanhoPadrao = await _tamanhoService.ObterComPredicate(c => c.PadraoSistema);

                        if (tamanhoPadrao != null)
                        {
                            listaTamanhoPadrao.Add(tamanhoPadrao.Id);

                            tamanhosProduto = listaTamanhoPadrao;
                        }
                    }

                    List<ProdutoCorTamanhoV2ViewModel> produtoCorTamanhosAdicionar = new();

                    foreach (var produtoCorV2ViewModel in produtoV2ViewModel.ProdutoCores)
                    {
                        #region ProdutoCor

                        var produtoCorId = Guid.Empty;

                        var produtoCor =
                                 await _produtoCorRepository?.Where(c => c.ProdutoId == produtoExistente.Id &&
                                                                         c.CorId == produtoCorV2ViewModel.CorId)
                                                                   ?.AsNoTracking()
                                                                   ?.FirstOrDefaultAsync();

                        if (produtoCor == null)
                        {
                            try
                            {
                                produtoCorV2ViewModel.ProdutoId = produtoExistente.Id;

                                produtoCorId = await _produtoCorV2Service.Cadastrar(produtoCorV2ViewModel);
                            }
                            catch (Exception ex)
                            {

                            }
                        }
                        else
                            produtoCorId = produtoCor.Id;

                        #endregion

                        #region ProdutoCorTamanho

                        var produtoCorTamanhos = tamanhosProduto.Join(
                            produtoCorV2ViewModel.ProdutoCorTamanhos,
                            tamId => tamId,
                            pct => pct.TamanhoId,
                            (tamId, pct) => new ProdutoCorTamanhoV2ViewModel
                            {
                                ProdutoCorId = produtoCorId,
                                TamanhoId = tamId,
                                Ativo = true,
                                EstoqueMinimo = pct?.EstoqueMinimo ?? 0,
                                EstoqueAtual = pct?.EstoqueAtual ?? 0,
                                Identificadores = pct != null
                                                ? pct.Identificadores
                                                : new(),
                                Caracteristicas = pct != null
                                                ? pct.Caracteristicas
                                                : new(),
                                TabelaPrecoProdutoCorTamanhos = pct?.TabelaPrecoProdutoCorTamanhos
                            });

                        if (produtoCorTamanhos.Any())
                            produtoCorTamanhosAdicionar.AddRange(produtoCorTamanhos);

                        #endregion

                        #region ProdutoCorImagem

                        if (produtoCorV2ViewModel.ProdutoCorImagens.Any())
                        {
                            foreach (var imagem in produtoCorV2ViewModel.ProdutoCorImagens)
                                imagem.ProdutoCorId = produtoCorId;

                            try
                            {
                                var listaProdutoCorImagem = await _produtoCorImagemV2Service.ListarPorCor(produtoCorId);

                                foreach (var produtoCorImagem in listaProdutoCorImagem)
                                    await _produtoCorImagemV2Service.Excluir(produtoCorImagem.Id);

                                await _produtoCorImagemV2Service.Cadastrar(produtoCorV2ViewModel.ProdutoCorImagens);
                            }
                            catch (Exception ex)
                            {

                            }
                        }

                        #endregion
                    }

                    #region ProdutoCorTamanho

                    foreach (var produtoCorTamanhoV2ViewModel in produtoCorTamanhosAdicionar)
                    {
                        var produtoCorTamanho =
                                await _produtoCorTamanhoRepository?.Where(c => c.ProdutoCorId == produtoCorTamanhoV2ViewModel.ProdutoCorId &&
                                                                               c.TamanhoId == produtoCorTamanhoV2ViewModel.TamanhoId)
                                                                  ?.AsNoTracking()
                                                                  ?.FirstOrDefaultAsync();

                        if (produtoCorTamanho == null)
                        {
                            try
                            {
                                var listaProdutoCorTamanho = new List<ProdutoCorTamanhoV2ViewModel>();
                                listaProdutoCorTamanho.Add(produtoCorTamanhoV2ViewModel);

                                await _produtoCorTamanhoV2Service.Cadastrar(listaProdutoCorTamanho.ToArray());

                                if (_notificador.PossuiAvisos() || _notificador.PossuiErros())
                                {
                                    _notificador.Limpar();

                                    produtoCorTamanhoV2ViewModel.Identificadores.CodigoGTINEAN = null;

                                    listaProdutoCorTamanho = new List<ProdutoCorTamanhoV2ViewModel>();
                                    listaProdutoCorTamanho.Add(produtoCorTamanhoV2ViewModel);

                                    await _produtoCorTamanhoV2Service.Cadastrar(listaProdutoCorTamanho.ToArray());
                                }
                            }
                            catch (Exception ex)
                            {
                            }
                        }
                    }

                    #endregion

                    produtoV2ViewModel.Id = produtoExistente.Id;
                    produtoId = produtoExistente.Id;

                    await _produtoV2Facade.AlterarCompleto(produtoV2ViewModel, false);
                }
            }
            catch (Exception ex)
            {
                if (ex.Message == "This SqlTransaction has completed; it is no longer usable.")
                {
                    var produtoExistente =
                        await _produtoV2Service.ObterComPredicate(c => c.Nome == produtoV2ViewModel.Nome);

                    if (produtoExistente != null)
                        produtoId = produtoExistente.Id;
                }
                else
                    await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = $"Erro ao cadastrar produto nome : {produtoV2ViewModel.Nome}" });
            }

            if (_notificador.PossuiAvisos() || _notificador.PossuiErros())
            {
                var listaNotificacaoAviso = _notificador.ObterNotificacoesAviso();

                foreach (var notificacaoAviso in listaNotificacaoAviso)
                    await _logErroService.Inserir(new LogErroInserirViewModel { Erro = notificacaoAviso.Mensagem, Dados = $"Aviso ao cadastrar produto nome: {produtoV2ViewModel.Nome}" });

                var listaNotificacaoErro = _notificador.ObterNotificacoesErro();

                foreach (var notificacaoErro in listaNotificacaoErro)
                    await _logErroService.Inserir(new LogErroInserirViewModel { Erro = notificacaoErro.Mensagem, Dados = $"Erro ao cadastrar produto nome: {produtoV2ViewModel.Nome}" });
            }

            if (produtoId != null)
            {
                produtoV2ViewModel.Id = produtoId.Value;

                await VincularProduto(produtoId.Value,
                                      produtoSnapshotViewModel.SiteId,
                                      identificacaoIntegracao);

                var listaProdutoCorTamanhoViewModel = await _produtoCorTamanhoV2Service.ObterListaProdutoCorTamanhoPorProdutoId(produtoId.Value);

                if (produtoSnapshotViewModel.ListaVariacao != null &&
                    listaProdutoCorTamanhoViewModel != null)
                {
                    foreach (var variacaoViewModel in produtoSnapshotViewModel.ListaVariacao)
                    {
                        ListaProdutoCorTamanhoViewModel produtoCorTamanhoViewModel = null;

                        if (variacaoViewModel.Cor != null &&
                            variacaoViewModel.Tamanho != null)
                        {
                            produtoCorTamanhoViewModel = listaProdutoCorTamanhoViewModel?.Where(c => c.Cor?.ToLower() == variacaoViewModel.Cor.Nome?.ToLower() &&
                                                                                                     c.Tamanho?.ToLower() == variacaoViewModel.Tamanho.Nome?.ToLower())
                                                                                        ?.FirstOrDefault();
                        }
                        else if (variacaoViewModel.Cor != null)
                        {
                            var tamanho = await _tamanhoService.ObterComPredicate(c => c.PadraoSistema);

                            if (tamanho != null)
                                produtoCorTamanhoViewModel = listaProdutoCorTamanhoViewModel?.Where(c => c.Cor?.ToLower() == variacaoViewModel.Cor.Nome?.ToLower() &&
                                                                                                         c.Tamanho?.ToLower() == tamanho.Descricao?.ToLower())
                                                                                            ?.FirstOrDefault();
                        }
                        else if (variacaoViewModel.Tamanho != null)
                        {
                            var cor = await _corService.ObterComPredicate(c => c.PadraoSistema);

                            if (cor != null)
                                produtoCorTamanhoViewModel = listaProdutoCorTamanhoViewModel?.Where(c => c.Cor?.ToLower() == cor.Descricao?.ToLower() &&
                                                                                                         c.Tamanho?.ToLower() == variacaoViewModel.Tamanho.Nome?.ToLower())
                                                                                            ?.FirstOrDefault();
                        }

                        if (produtoCorTamanhoViewModel != null)
                        {
                            #region Variação

                            await VincularVariacao(produtoCorTamanhoViewModel.Id.Value,
                                                   variacaoViewModel.SiteId,
                                                   identificacaoIntegracao);

                            #endregion
                        }
                    }
                }
            }

            #endregion

            if (produtoId.HasValue)
                await _produtoCorrecaoService.RecriarVariacoesProdutos(new() { produtoId.Value });

			return produtoV2ViewModel;
        }

        public async Task<Guid?> CadastrarAlterarMarca(
            EntidadeSnapshotViewModel marca,
            int identificacaoIntegracao,
            string url)
        {
            Guid? marcaId = null;

            try
            {
                PrepararConfiguracao(identificacaoIntegracao, url);

                var marcaExistente =
                    await _marcaService.ObterComPredicate(c => c.Nome == marca.Nome);

                if (marcaExistente == null)
                {
                    var marcaCadastrar = new MarcaViewModel()
                    {
                        Nome = marca.Nome,
                        Ativo = true
                    };

                    marcaId = await _marcaService.Cadastrar(marcaCadastrar);
                }
                else
                    marcaId = marcaExistente.Id;

                if (marcaId != null)
                    await VincularMarca(marcaId.Value,
                                        marca.SiteId,
                                        identificacaoIntegracao);
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = $"Erro ao cadastrar marca: {marca.Nome}" });
            }

            return marcaId;
        }

        public async Task<Guid?> CadastrarAlterarCor(
            EntidadeSnapshotViewModel cor,
            int identificacaoIntegracao,
            string url)
        {
            Guid? corId = null;

            try
            {
                PrepararConfiguracao(identificacaoIntegracao, url);

                if (cor == null)
                    return Guid.Empty;

                var corPadrao = false;

                var corExistente =
                    await _corService.ObterComPredicate(c => c.Descricao == cor.Nome);

                if (corExistente == null)
                {
                    var corCadastrar = new CorViewModel()
                    {
                        Descricao = cor.Nome,
                        DescricaoEcommerce = cor.NomeEcommerce,
                        Ativo = true,
                        Imagem = cor.Imagem
                    };

                    corId =
                        await _corService.CadastrarPelaTela(corCadastrar);
                }
                else
                {
                    corId = corExistente.Id;
                    corPadrao = corExistente.PadraoSistema;

                    await _corService.AlterarImagem(corId.Value, null, cor.Imagem);
                }

                if (corId != null && !corPadrao)
                    await VincularCaracteristica(corId.Value,
                                                 cor.SiteId,
                                                 identificacaoIntegracao);
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = $"Erro ao cadastrar cor: {cor.Nome}" });
            }

            return corId;
        }

        public async Task<Guid?> CadastrarAlterarTamanho(
            EntidadeSnapshotViewModel tamanho,
            int identificacaoIntegracao,
            string url)
        {
            Guid? tamanhoId = null;

            try
            {
                PrepararConfiguracao(identificacaoIntegracao, url);

                if (tamanho == null)
                    return Guid.Empty;

                var tamanhoPadrao = false;

                var tamanhoExistente =
                    await _tamanhoService.ObterComPredicate(c => c.Descricao == tamanho.Nome);

                if (tamanhoExistente == null)
                {
                    var tamanhoCadastrar = new TamanhoViewModel()
                    {
                        Descricao = tamanho.Nome,
                        DescricaoEcommerce = tamanho.NomeEcommerce,
                        Ativo = true
                    };

                    tamanhoId =
                        await _tamanhoService.CadastrarPelaTela(tamanhoCadastrar);
                }
                else
                {
                    tamanhoId = tamanhoExistente.Id;
                    tamanhoPadrao = tamanhoExistente.PadraoSistema;
                }

                if (tamanhoId != null && !tamanhoPadrao)
                    await VincularCaracteristica(tamanhoId.Value,
                                                 tamanho.SiteId,
                                                 identificacaoIntegracao);
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = $"Erro ao cadastrar tamanho: {tamanho.Nome}" });
            }

            return tamanhoId;
        }

        public async Task<Guid?> CadastrarAlterarListaCategoria(
            List<CategoriaSnapshotViewModel> listaCategoria,
            int identificacaoIntegracao,
            string url)
        {
            Guid? ultimaCategoriaZendarId = null;

            try
            {
                PrepararConfiguracao(identificacaoIntegracao, url);

                Dictionary<string, CategoriaSnapshotViewModel> listaCategoriaHash = new Dictionary<string, CategoriaSnapshotViewModel>();

                foreach (var categoria in listaCategoria)
                    listaCategoriaHash[categoria.SiteId] = categoria;

                foreach (var categoria in listaCategoria)
                {
                    Guid? categoriaZendarId = null;

                    if (string.IsNullOrEmpty(categoria.SitePaiId))
                    {
                        var categoriaExistente =
                            await _categoriaProdutoService.ObterComPredicate(c => c.Nome == categoria.Nome);

                        if (categoriaExistente == null)
                        {
                            var categoriaCadastrar = new CategoriaProdutoViewModel()
                            {
                                Nome = categoria.Nome,
                                Ativo = true
                            };

                            try
                            {
                                categoriaZendarId =
                                    await _categoriaProdutoService.Cadastrar(categoriaCadastrar);
                            }
                            catch (Exception ex)
                            {
                                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = $"Erro ao cadastrar categoria: {categoria.Nome}" });
                            }
                        }
                        else
                            categoriaZendarId = categoriaExistente.Id;

                        categoria.ZendarId = categoriaZendarId;

                        ultimaCategoriaZendarId = categoriaZendarId;
                    }
                    else
                    {
                        var categoriaPai = listaCategoriaHash[categoria.SitePaiId];

                        var categoriaExistente =
                            await _categoriaProdutoService.ObterComPredicate(c => c.Nome == categoria.Nome &&
                                                                                  c.CategoriaProdutoPaiId == categoriaPai.ZendarId);

                        if (categoriaExistente == null)
                        {
                            var categoriaCadastrar = new CategoriaProdutoViewModel()
                            {
                                Nome = categoria.Nome,
                                Ativo = true,
                                CategoriaProdutoPaiId = categoriaPai.ZendarId
                            };

                            try
                            {
                                categoriaZendarId =
                                    await _categoriaProdutoService.Cadastrar(categoriaCadastrar);
                            }
                            catch (Exception ex)
                            {
                                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = $"Erro ao cadastrar categoria: {categoria.Nome}" });
                            }
                        }
                        else
                            categoriaZendarId = categoriaExistente.Id;

                        categoria.ZendarId = categoriaZendarId;

                        ultimaCategoriaZendarId = categoriaZendarId;
                    }
                }

                await VincularCategoria(listaCategoriaHash,
                                        identificacaoIntegracao);
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar categoria recursiva." });
            }

            return ultimaCategoriaZendarId;
        }

        #endregion

        #region Private Methods

        private void PrepararConfiguracao(
            int identificacaoIntegracao,
            string url)
        {
            _aspNetUserInfo.HabilitarTrigger(false);

            if (identificacaoIntegracao == (int)IdentificacaoIntegracao.TRAY)
            {
                var httpClient = new HttpClient(_syncJwtHandler) { BaseAddress = new Uri(url) };

                _zendarSyncTrayApi = RestService.For<IZendarSyncEcommerceService<ITrayApi>>(httpClient);
            }
        }

        private async Task<List<ProdutoPrecoLojaV2ViewModel>> PrepararListaProdutoPrecoLoja(
            decimal precoCusto,
            decimal precoVenda)
        {
            var listaProdutoPrecoLojaV2ViewModel = new List<ProdutoPrecoLojaV2ViewModel>();

            try
            {
                var listaLoja = await _lojaService.ListarSelect();

                foreach (var loja in listaLoja)
                {
                    ProdutoPrecoLojaV2ViewModel produtoPrecoLojaV2ViewModel = new ProdutoPrecoLojaV2ViewModel();
                    produtoPrecoLojaV2ViewModel.LojaId = loja.Id.Value;
                    produtoPrecoLojaV2ViewModel.PrecoCompra = precoCusto;
                    produtoPrecoLojaV2ViewModel.PrecoCusto = precoCusto;

                    #region Preco Venda

                    var precoVendaV2ViewModel = new PrecoVendaV2ViewModel();
                    precoVendaV2ViewModel.PrecoVenda = precoVenda;

                    if (precoCusto > 1)
                        precoVendaV2ViewModel.Markup = Math.Round((precoVenda / precoCusto - 1) * 100, 4);

                    produtoPrecoLojaV2ViewModel.PrecoVenda = precoVendaV2ViewModel;

                    #endregion

                    listaProdutoPrecoLojaV2ViewModel.Add(produtoPrecoLojaV2ViewModel);
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar produto preço loja." });
            }

            return listaProdutoPrecoLojaV2ViewModel;
        }

        private async Task<List<TabelaPrecoProdutoV2ViewModel>> PrepararListaTabelaPrecoProduto(
            decimal precoCusto,
            decimal precoVenda,
            Guid? produtoId,
            Guid? tabelaPrecoId,
			Guid tabelaPrecoPadraoId)
        {
            var listaTabelaPrecoProdutoV2ViewModel = new List<TabelaPrecoProdutoV2ViewModel>();

            try
            {
				if (tabelaPrecoId.HasValue && tabelaPrecoPadraoId != tabelaPrecoId)
                {
                    if (tabelaPrecoId != null)
                    {
                        TabelaPrecoProdutoV2ViewModel tabelaPrecoProdutoV2ViewModel = new TabelaPrecoProdutoV2ViewModel();
                        tabelaPrecoProdutoV2ViewModel.TabelaPrecoId = tabelaPrecoId.Value;

                        var precoVendaV2ViewModel = new PrecoVendaV2ViewModel();
                        precoVendaV2ViewModel.PrecoVenda = precoVenda;

                        if (precoCusto > 1)
                            precoVendaV2ViewModel.Markup = Math.Round((precoVenda / precoCusto - 1) * 100, 4);

                        tabelaPrecoProdutoV2ViewModel.PrecoVenda = precoVendaV2ViewModel;

                        listaTabelaPrecoProdutoV2ViewModel.Add(tabelaPrecoProdutoV2ViewModel);
                    }
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar tabela preço produto loja." });
            }

            return listaTabelaPrecoProdutoV2ViewModel;
        }

        private async Task<List<ProdutoEcommerceV2ViewModel>> PrepararListaProdutoEcommerce(
            ProdutoSnapshotViewModel produtoSnapshotViewModel,
            int identificacaoIntegracao)
        {
            var listaProdutoEcommerceV2ViewModel = new List<ProdutoEcommerceV2ViewModel>();

            try
            {
                ProdutoEcommerceV2ViewModel produtoEcommerceV2ViewModel = new ProdutoEcommerceV2ViewModel();
                produtoEcommerceV2ViewModel.Titulo = produtoSnapshotViewModel.Nome;
                produtoEcommerceV2ViewModel.Descricao = produtoSnapshotViewModel.Descricao;
                produtoEcommerceV2ViewModel.Anunciado = produtoSnapshotViewModel.Anunciado;
                produtoEcommerceV2ViewModel.ItensInclusos = produtoSnapshotViewModel.DescricaoItemIncluso;
                produtoEcommerceV2ViewModel.TempoGarantia = produtoSnapshotViewModel.DescricaoGarantia;
                produtoEcommerceV2ViewModel.DisponibilidadeEntrega = produtoSnapshotViewModel.DescricaoDisponibilidade;
                produtoEcommerceV2ViewModel.IdentificacaoIntegracao = (IdentificacaoIntegracao)identificacaoIntegracao;
                produtoEcommerceV2ViewModel.Campo1 = produtoSnapshotViewModel.Campo1 ?? "cor";
                produtoEcommerceV2ViewModel.Campo2 = produtoSnapshotViewModel.Campo2 ?? "tamanho";
                produtoEcommerceV2ViewModel.Referencia = produtoSnapshotViewModel.SiteId;

                listaProdutoEcommerceV2ViewModel.Add(produtoEcommerceV2ViewModel);
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar produto ecommerce." });
            }

            return listaProdutoEcommerceV2ViewModel;
        }

        private async Task<List<ProdutoCorV2ViewModel>> PrepararListaProdutoVariacao(
            ProdutoSnapshotViewModel produtoSnapshotViewModel,
            int identificacaoIntegracao,
            Guid? tabelaPrecoId,
            string url)
        {
            var listaProdutoCorV2ViewModel = new List<ProdutoCorV2ViewModel>();

            try
            {
                if (produtoSnapshotViewModel.ListaVariacao != null &&
                    produtoSnapshotViewModel.ListaVariacao.Count > 0)
                {
                    var corPadrao =
                        await _corService.ObterComPredicate(c => c.PadraoSistema);

                    if (corPadrao == null)
                        return null;

                    var tamanhoPadrao =
                        await _tamanhoService.ObterComPredicate(c => c.PadraoSistema);

                    if (tamanhoPadrao == null)
                        return null;

                    var listaVariacaoViewModel = new List<VariacaoSnapshotViewModel>();

                    ProdutoCorV2ViewModel produtoCorV2ViewModel = new ProdutoCorV2ViewModel();

                    #region Cor e Tamanho

                    foreach (var variacaoAgrupadaViewModel in produtoSnapshotViewModel?.ListaVariacao?.Where(c => c.Cor != null &&
                                                                                                                  !string.IsNullOrEmpty(c.Cor.Nome) &&
                                                                                                                  c.Tamanho != null &&
                                                                                                                  !string.IsNullOrEmpty(c.Tamanho.Nome))
                                                                                                     ?.GroupBy(c => c.Cor.SiteId)
                                                                                                     ?.ToList())
                    {
                        produtoCorV2ViewModel = new ProdutoCorV2ViewModel();
                        produtoCorV2ViewModel.Ativo = true;

                        var variacaoViewModel = variacaoAgrupadaViewModel.FirstOrDefault();

                        #region Imagem

                        var listaProdutoCorImagemV2ViewModel =
                            await PrepararListaProdutoCorImagem(variacaoViewModel.ListaImagem);

                        if (listaProdutoCorImagemV2ViewModel != null &&
                            listaProdutoCorImagemV2ViewModel.Count > 0)
                            produtoCorV2ViewModel.ProdutoCorImagens = listaProdutoCorImagemV2ViewModel?.ToArray();

                        #endregion

                        #region Cor

                        var corSnapshot = variacaoViewModel.Cor;

                        var corId =
                            await CadastrarAlterarCor(corSnapshot,
                                                      identificacaoIntegracao,
                                                      url);

                        produtoCorV2ViewModel.CorId = corId.Value;

                        #endregion

                        #region Tamanho

                        listaVariacaoViewModel = variacaoAgrupadaViewModel?.Where(c => c.Cor != null &&
                                                                                       !string.IsNullOrEmpty(c.Cor.Nome) &&
                                                                                       c.Tamanho != null &&
                                                                                       !string.IsNullOrEmpty(c.Tamanho.Nome))
                                                                          ?.ToList();

                        if (listaVariacaoViewModel != null &&
                            listaVariacaoViewModel.Count > 0)
                        {
                            var listaProdutoCorTamanhoViewModel =
                                await PrepararListaProdutoCor(produtoSnapshotViewModel,
                                                              listaVariacaoViewModel,
                                                              identificacaoIntegracao,
                                                              tabelaPrecoId,
                                                              url);

                            if (listaProdutoCorTamanhoViewModel != null &&
                                listaProdutoCorTamanhoViewModel.Count > 0)
                            {
                                produtoCorV2ViewModel.ProdutoCorTamanhos = listaProdutoCorTamanhoViewModel?.ToArray();

                                listaProdutoCorV2ViewModel.Add(produtoCorV2ViewModel);
                            }
                        }

                        #endregion
                    }

                    #endregion

                    #region Somente Cor

                    foreach (var variacaoViewModel in produtoSnapshotViewModel.ListaVariacao?.Where(c => c.Cor != null &&
                                                                                                         !string.IsNullOrEmpty(c.Cor.Nome) &&
                                                                                                         c.Tamanho == null))
                    {
                        produtoCorV2ViewModel = new ProdutoCorV2ViewModel();
                        produtoCorV2ViewModel.Ativo = true;

                        #region Imagem

                        var listaProdutoCorImagemV2ViewModel =
                            await PrepararListaProdutoCorImagem(variacaoViewModel.ListaImagem);

                        if (listaProdutoCorImagemV2ViewModel != null &&
                            listaProdutoCorImagemV2ViewModel.Count > 0)
                            produtoCorV2ViewModel.ProdutoCorImagens = listaProdutoCorImagemV2ViewModel?.ToArray();

                        #endregion

                        #region Cor

                        var corSnapshot = variacaoViewModel.Cor;

                        var corId =
                            await CadastrarAlterarCor(corSnapshot,
                                                      identificacaoIntegracao,
                                                      url);

                        produtoCorV2ViewModel.CorId = corId.Value;

                        #endregion

                        #region Tamanho Padrão

                        var listaProdutoCorTamanhoViewModel =
                            await PrepararListaProdutoCorTamanho(identificacaoIntegracao,
                                                                 tamanhoPadrao.Id,
                                                                 tabelaPrecoId,
                                                                 produtoSnapshotViewModel,
                                                                 produtoSnapshotViewModel.CodigoGTINEAN,
                                                                 produtoSnapshotViewModel.Altura,
                                                                 produtoSnapshotViewModel.Largura,
                                                                 produtoSnapshotViewModel.Profundidade,
                                                                 produtoSnapshotViewModel.PesoLiquido,
                                                                 produtoSnapshotViewModel.PesoBruto,
                                                                 produtoSnapshotViewModel.PesoEmbalagem,
                                                                 produtoSnapshotViewModel.EstoqueMinimo,
                                                                 variacaoViewModel.EstoqueAtual,
                                                                 variacaoViewModel.PrecoCusto,
                                                                 variacaoViewModel.PrecoVenda);

                        if (listaProdutoCorTamanhoViewModel != null &&
                            listaProdutoCorTamanhoViewModel.Count > 0)
                        {
                            produtoCorV2ViewModel.ProdutoCorTamanhos = listaProdutoCorTamanhoViewModel?.ToArray();

                            listaProdutoCorV2ViewModel.Add(produtoCorV2ViewModel);
                        }

                        #endregion
                    }

                    #endregion

                    #region Somente Tamanho

                    listaVariacaoViewModel = produtoSnapshotViewModel.ListaVariacao?.Where(c => c.Tamanho != null &&
                                                                                                !string.IsNullOrEmpty(c.Tamanho.Nome) &&
                                                                                                c.Cor == null)
                                                                                   ?.ToList();

                    if (listaVariacaoViewModel != null &&
                        listaVariacaoViewModel.Count > 0)
                    {
                        produtoCorV2ViewModel = new ProdutoCorV2ViewModel();
                        produtoCorV2ViewModel.Ativo = true;

                        #region Imagem

                        foreach (var variacaoViewModel in listaVariacaoViewModel)
                        {
                            var listaProdutoCorImagemV2ViewModel =
                                await PrepararListaProdutoCorImagem(variacaoViewModel.ListaImagem);

                            if (listaProdutoCorImagemV2ViewModel != null &&
                                listaProdutoCorImagemV2ViewModel.Count > 0)
                                produtoCorV2ViewModel.ProdutoCorImagens = listaProdutoCorImagemV2ViewModel?.ToArray();
                        }

                        #endregion

                        #region Cor Padrão

                        produtoCorV2ViewModel.CorId = corPadrao.Id;

                        #endregion

                        #region Tamanho

                        var listaProdutoCorTamanhoViewModel =
                            await PrepararListaProdutoCor(produtoSnapshotViewModel,
                                                          listaVariacaoViewModel,
                                                          identificacaoIntegracao,
                                                          tabelaPrecoId,
                                                          url);

                        if (listaProdutoCorTamanhoViewModel != null &&
                            listaProdutoCorTamanhoViewModel.Count > 0)
                        {
                            produtoCorV2ViewModel.ProdutoCorTamanhos = listaProdutoCorTamanhoViewModel?.ToArray();

                            listaProdutoCorV2ViewModel.Add(produtoCorV2ViewModel);
                        }

                        #endregion
                    }

                    #endregion
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar produto cor." });
            }

            return listaProdutoCorV2ViewModel;
        }

        private async Task<List<ProdutoCorTamanhoV2ViewModel>> PrepararListaProdutoCor(
            ProdutoSnapshotViewModel produtoSnapshotViewModel,
            List<VariacaoSnapshotViewModel> listaVariacaoViewModel,
            int identificacaoIntegracao,
            Guid? tabelaPrecoId,
            string url)
        {
            var listaProdutoCorTamanhoViewModel = new List<ProdutoCorTamanhoV2ViewModel>();

            try
            {
                foreach (var variacaoViewModel in listaVariacaoViewModel)
                {
                    #region Tamanho

                    EntidadeSnapshotViewModel tamanhoSnapshot = variacaoViewModel.Tamanho;

                    var tamanhoId =
                        await CadastrarAlterarTamanho(tamanhoSnapshot,
                                                      identificacaoIntegracao,
                                                      url);

                    #endregion

                    listaProdutoCorTamanhoViewModel.AddRange(
                        await PrepararListaProdutoCorTamanho(identificacaoIntegracao,
                                                             tamanhoId.Value,
                                                             tabelaPrecoId,
                                                             produtoSnapshotViewModel,
                                                             variacaoViewModel.CodigoGTINEAN,
                                                             variacaoViewModel.Altura,
                                                             variacaoViewModel.Largura,
                                                             variacaoViewModel.Profundidade,
                                                             variacaoViewModel.PesoLiquido,
                                                             variacaoViewModel.PesoBruto,
                                                             variacaoViewModel.PesoEmbalagem,
                                                             variacaoViewModel.EstoqueMinimo,
                                                             variacaoViewModel.EstoqueAtual,
                                                             variacaoViewModel.PrecoCusto,
                                                             variacaoViewModel.PrecoVenda));
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar produto cor." });
            }

            return listaProdutoCorTamanhoViewModel;
        }

        private async Task<List<ProdutoCorTamanhoV2ViewModel>> PrepararListaProdutoCorTamanho(
            int identificacaoIntegracao,
            Guid tamanhoId,
            Guid? tabelaPrecoId,
            ProdutoSnapshotViewModel produtoSnapshotViewModel,
            string codigoGTINEAN,
            decimal altura,
            decimal largura,
            decimal profundidade,
            decimal pesoLiquido,
            decimal pesoBruto,
            decimal pesoEmbalagem,
            decimal estoqueMinimo,
            decimal estoqueAtual,
            decimal precoCusto,
            decimal precoVenda)
        {
            var listaProdutoCorTamanhoV2ViewModel = new List<ProdutoCorTamanhoV2ViewModel>();

            try
            {
                ProdutoCorTamanhoV2ViewModel produtoCorTamanhoV2ViewModel = new ProdutoCorTamanhoV2ViewModel();
                produtoCorTamanhoV2ViewModel.TamanhoId = tamanhoId;
                produtoCorTamanhoV2ViewModel.EstoqueMinimo = estoqueMinimo;
                produtoCorTamanhoV2ViewModel.EstoqueAtual = estoqueAtual;
                produtoCorTamanhoV2ViewModel.Ativo = true;

                var integracao =
                   await _integracaoService.Obter((IdentificacaoIntegracao)identificacaoIntegracao);

                if (integracao != null)
                    produtoCorTamanhoV2ViewModel.LocalEstoqueId = integracao.LocalEstoqueId;

                #region Produto Cor Tamanho Caracteristicas

                ProdutoCorTamanhoCaracteristicasV2ViewModel produtoCorTamanhoCaracteristicasV2ViewModel = new ProdutoCorTamanhoCaracteristicasV2ViewModel();
                produtoCorTamanhoCaracteristicasV2ViewModel.Altura = altura;
                produtoCorTamanhoCaracteristicasV2ViewModel.Largura = largura;
                produtoCorTamanhoCaracteristicasV2ViewModel.Profundidade = profundidade;
                produtoCorTamanhoCaracteristicasV2ViewModel.PesoLiquido = pesoLiquido;
                produtoCorTamanhoCaracteristicasV2ViewModel.PesoBruto = pesoBruto;
                produtoCorTamanhoCaracteristicasV2ViewModel.PesoEmbalagem = pesoEmbalagem;

                produtoCorTamanhoV2ViewModel.Caracteristicas = produtoCorTamanhoCaracteristicasV2ViewModel;

                #endregion

                #region Produto Cor Tamanho Identificadores

                ProdutoCorTamanhoIdentificadoresV2ViewModel produtoCorTamanhoIdentificadoresV2ViewModel = new ProdutoCorTamanhoIdentificadoresV2ViewModel();
                produtoCorTamanhoIdentificadoresV2ViewModel.CodigoGTINEAN = codigoGTINEAN;

                produtoCorTamanhoV2ViewModel.Identificadores = produtoCorTamanhoIdentificadoresV2ViewModel;

                #endregion

                #region Tabela Preco Produto Cor Tamanho

                var listaTabelaPrecoProdutoCorTamanho =
                    await PrepararListaTabelaPrecoProdutoCorTamanho(produtoSnapshotViewModel,
                                                                    precoCusto,
                                                                    precoVenda,
                                                                    tabelaPrecoId);

                if (listaTabelaPrecoProdutoCorTamanho != null &&
                    listaTabelaPrecoProdutoCorTamanho.Count > 0)
                    produtoCorTamanhoV2ViewModel.TabelaPrecoProdutoCorTamanhos = listaTabelaPrecoProdutoCorTamanho?.ToArray();

                #endregion

                listaProdutoCorTamanhoV2ViewModel.Add(produtoCorTamanhoV2ViewModel);

                if (listaProdutoCorTamanhoV2ViewModel != null)
                {
                    if (listaProdutoCorTamanhoV2ViewModel?.Where(c => c.TamanhoId == tamanhoId &&
                                                                      c.Identificadores?.CodigoGTINEAN == produtoCorTamanhoV2ViewModel.Identificadores?.CodigoGTINEAN &&
                                                                      c.Caracteristicas?.Altura == produtoCorTamanhoV2ViewModel.Caracteristicas?.Altura &&
                                                                      c.Caracteristicas?.Largura == produtoCorTamanhoV2ViewModel.Caracteristicas?.Largura &&
                                                                      c.Caracteristicas?.Profundidade == produtoCorTamanhoV2ViewModel.Caracteristicas?.Profundidade &&
                                                                      c.Caracteristicas?.PesoLiquido == produtoCorTamanhoV2ViewModel.Caracteristicas?.PesoLiquido &&
                                                                      c.Caracteristicas?.PesoBruto == produtoCorTamanhoV2ViewModel.Caracteristicas?.PesoBruto &&
                                                                      c.Caracteristicas?.PesoEmbalagem == produtoCorTamanhoV2ViewModel.Caracteristicas?.PesoEmbalagem &&
                                                                      c.EstoqueMinimo == produtoCorTamanhoV2ViewModel.EstoqueMinimo &&
                                                                      c.EstoqueAtual == produtoCorTamanhoV2ViewModel.EstoqueAtual)
                                                         ?.FirstOrDefault() == null)
                        listaProdutoCorTamanhoV2ViewModel.Add(produtoCorTamanhoV2ViewModel);

                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar produto cor tamanho." });
            }

            return listaProdutoCorTamanhoV2ViewModel;
        }

        private async Task<List<ProdutoCorImagemV2ViewModel>> PrepararListaProdutoCorImagem(
            List<ImagemSnapshotViewModel> listaImagem)
        {
            var listaProdutoCorImagemV2ViewModel = new List<ProdutoCorImagemV2ViewModel>();

            try
            {
                if (listaImagem != null &&
                    listaImagem.Count > 0)
                {
                    var contador = 1;

                    foreach (var imagem in listaImagem)
                    {
                        var produtoCorImagemV2ViewModel = new ProdutoCorImagemV2ViewModel()
                        {
                            Imagem = imagem.Imagem,
                            Principal = imagem.Principal,
                            SequenciaOrdenacao = contador
                        };

                        if (listaProdutoCorImagemV2ViewModel.FirstOrDefault(c => c.Imagem == imagem.Imagem) == null)
                            listaProdutoCorImagemV2ViewModel.Add(produtoCorImagemV2ViewModel);

                        contador++;
                    }
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar produto cor imagem." });
            }

            return listaProdutoCorImagemV2ViewModel;
        }

        private async Task<List<TabelaPrecoProdutoCorTamanhoV2ViewModel>> PrepararListaTabelaPrecoProdutoCorTamanho(
            ProdutoSnapshotViewModel produtoSnapshotViewModel,
            decimal precoCusto,
            decimal precoVenda,
            Guid? tabelaPrecoId)
        {
            var listaTabelaPrecoProdutoCorTamanhoV2ViewModel = new List<TabelaPrecoProdutoCorTamanhoV2ViewModel>();

            try
            {
                if (tabelaPrecoId != null &&
                    produtoSnapshotViewModel.PrecoVenda != precoVenda)
                {
                    TabelaPrecoProdutoCorTamanhoV2ViewModel tabelaPrecoProdutoCorTamanhoV2ViewModel = new TabelaPrecoProdutoCorTamanhoV2ViewModel();
                    tabelaPrecoProdutoCorTamanhoV2ViewModel.TabelaPrecoId = tabelaPrecoId.Value;

                    #region PrecoVenda

                    var precoVendaV2ViewModel = new PrecoVendaV2ViewModel();
                    precoVendaV2ViewModel.PrecoVenda = precoVenda;

                    if (precoCusto > 1)
                        precoVendaV2ViewModel.Markup = Math.Round((precoVenda / precoCusto - 1) * 100, 4);

                    tabelaPrecoProdutoCorTamanhoV2ViewModel.PrecoVenda = precoVendaV2ViewModel;

                    #endregion

                    listaTabelaPrecoProdutoCorTamanhoV2ViewModel.Add(tabelaPrecoProdutoCorTamanhoV2ViewModel);
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao preparar tabela preço cor tamanho." });
            }

            return listaTabelaPrecoProdutoCorTamanhoV2ViewModel;
        }

        private async Task VincularProduto(
            Guid id,
            string siteId,
            int identificacaoIntegracao)
        {
            try
            {
                if (!string.IsNullOrEmpty(siteId))
                {
                    var vincularCadastroRequest = new VincularCadastroRequest();
                    vincularCadastroRequest.ZendarId = id;
                    vincularCadastroRequest.SiteId = siteId;

                    if (identificacaoIntegracao == (int)IdentificacaoIntegracao.TRAY)
                        await _zendarSyncTrayApi.VincularProduto(vincularCadastroRequest);
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao vincular produto." });
            }
        }

        private async Task VincularMarca(
            Guid id,
            string siteId,
            int identificacaoIntegracao)
        {
            try
            {
                if (!string.IsNullOrEmpty(siteId))
                {
                    var vincularCadastroRequest = new VincularCadastroRequest();
                    vincularCadastroRequest.ZendarId = id;
                    vincularCadastroRequest.SiteId = siteId;

                    if (identificacaoIntegracao == (int)IdentificacaoIntegracao.TRAY)
                        await _zendarSyncTrayApi.VincularMarca(vincularCadastroRequest);
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao vincular marca." });
            }
        }

        private async Task VincularCaracteristica(
            Guid id,
            string siteId,
            int identificacaoIntegracao)
        {
            try
            {
                if (!string.IsNullOrEmpty(siteId))
                {
                    var vincularCadastroRequest = new VincularCadastroRequest();
                    vincularCadastroRequest.ZendarId = id;
                    vincularCadastroRequest.SiteId = siteId;

                    if (identificacaoIntegracao == (int)IdentificacaoIntegracao.TRAY)
                        await _zendarSyncTrayApi.VincularCaracteristica(vincularCadastroRequest);
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao vincular caracteristica." });
            }
        }

        private async Task VincularVariacao(
            Guid id,
            string siteId,
            int identificacaoIntegracao)
        {
            try
            {
                if (!string.IsNullOrEmpty(siteId))
                {
                    var vincularCadastroRequest = new VincularCadastroRequest();
                    vincularCadastroRequest.ZendarId = id;
                    vincularCadastroRequest.SiteId = siteId;

                    if (identificacaoIntegracao == (int)IdentificacaoIntegracao.TRAY)
                        await _zendarSyncTrayApi.VincularVariacao(vincularCadastroRequest);
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao vincular variação." });
            }
        }

        private async Task VincularCategoria(
            Dictionary<string, CategoriaSnapshotViewModel> listaCategoriaHash,
            int identificacaoIntegracao)
        {
            try
            {
                foreach (var categoriaHash in listaCategoriaHash)
                {
                    var categoria = categoriaHash.Value;

                    if (!string.IsNullOrEmpty(categoria.SiteId))
                    {
                        var vincularCadastroRequest = new VincularCadastroRequest();
                        vincularCadastroRequest.ZendarId = categoria.ZendarId.Value;
                        vincularCadastroRequest.SiteId = categoria.SiteId.ToString();

                        if (identificacaoIntegracao == (int)IdentificacaoIntegracao.TRAY)
                            await _zendarSyncTrayApi.VincularCategoria(vincularCadastroRequest);
                    }
                }
            }
            catch (Exception ex)
            {
                await _logErroService.Inserir(new LogErroInserirViewModel { Erro = ex.Message, Dados = "Erro ao vincular categoria." });
            }
        }

        #endregion
    }
}
