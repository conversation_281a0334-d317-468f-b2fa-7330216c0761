﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.ViewModels.V1.ImportacaoCadastroIntegracaoViewModels;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Repository.Aplicacao.IntegracaoRepositories.IntegracaoRepository;

namespace Zendar.Business.Services.V1.ImportacaoCadastroIntegracaoService
{
	public class ImportacaoCadastroIntegracaoService : BaseService, IImportacaoCadastroIntegracaoService
	{
		private readonly IIntegracaoRepository _integracaoRepository;
		private readonly IImportacaoCadastroIntegracaoRepository _importacaoCadastroIntegracaoRepository;

		public ImportacaoCadastroIntegracaoService(
			INotificador notificador,
			IIntegracaoRepository integracaoRepository,
			IImportacaoCadastroIntegracaoRepository importacaoCadastroIntegracaoRepository)
			: base(notificador)
		{
			_importacaoCadastroIntegracaoRepository = importacaoCadastroIntegracaoRepository;
			_integracaoRepository = integracaoRepository;
		}

		public void Dispose()
		{
			_integracaoRepository?.Dispose();
			_importacaoCadastroIntegracaoRepository?.Dispose();
		}

		public async Task EnviarProduto(
			Guid produtoId,
			Guid categoriaId)
		{
			var integracoes = await _integracaoRepository.FindAllSelectAsNoTracking(
				i => i.IdentificacaoIntegracao == IdentificacaoIntegracao.FOMER_DELIVERY &&
					 i.ImportacaoCadastrosIntegracao.Any(c => c.CategoriaProdutoId == categoriaId),
				i => new Data.Models.Aplicacao.Integracao
				{
					Id = i.Id,
					LojaId = i.LojaId
				});

			if (!integracoes.Any()) return;

			List<ImportacaoCadastroIntegracao> produtosImportacao = new();			

			foreach (var integracao in integracoes)
			{
				var sequenciaOrdenacao =
					await _importacaoCadastroIntegracaoRepository.ObterSequenciaOrdenacaoProduto(categoriaId, integracao.Id);

				var produtoImportacao = new ImportacaoCadastroIntegracao
				{
					CategoriaProdutoId = categoriaId,
					ProdutoId = produtoId,
					LojaId = integracao.LojaId,
					IntegracaoId = integracao.Id,
					TipoCadastro = TipoCadastroImportacaoIntegracao.PRODUTO,
					SequenciaOrdenacao = ++sequenciaOrdenacao,
				};

				produtosImportacao.Add(produtoImportacao);
			}

			await _importacaoCadastroIntegracaoRepository.InsertRange(produtosImportacao);
		}

		public async Task AlterarProduto(Guid produtoId, Guid novaCategoriaId, Guid antigaCategoriaId)
		{
			var produtos = await _importacaoCadastroIntegracaoRepository
				.Where(x => x.ProdutoId == produtoId &&
							x.CategoriaProdutoId == antigaCategoriaId &&
							x.Integracao.IdentificacaoIntegracao == IdentificacaoIntegracao.FOMER_DELIVERY)
				.ToListAsync();

			await _importacaoCadastroIntegracaoRepository.DeleteRange(produtos);

			await EnviarProduto(produtoId, novaCategoriaId);
		}

		public async Task<bool> ValidarSeCategoriaExiste(
			Guid categoriaId)
		{
			return await _importacaoCadastroIntegracaoRepository.Any(
				i => i.CategoriaProdutoId == categoriaId &&
					 i.TipoCadastro == TipoCadastroImportacaoIntegracao.CATEGORIA);
		}

		public async Task EnviarCategoria(
			Guid categoriaId)
		{
			var integracoes = await _integracaoRepository.FindAllSelectAsNoTracking(
				i => i.IdentificacaoIntegracao == IdentificacaoIntegracao.FOMER_DELIVERY,
				i => new Data.Models.Aplicacao.Integracao
				{
					Id = i.Id,
					LojaId = i.LojaId
				});

			List<ImportacaoCadastroIntegracao> categoriasImportacao = new();

			foreach (var integracao in integracoes)
			{
				var sequenciaOrdenacao =
					await _importacaoCadastroIntegracaoRepository.ObterSequenciaOrdenacaoCategoria(integracao.Id);

				var categoriaImportacao = new ImportacaoCadastroIntegracao
				{
					CategoriaProdutoId = categoriaId,
					LojaId = integracao.LojaId,
					IntegracaoId = integracao.Id,
					TipoCadastro = TipoCadastroImportacaoIntegracao.CATEGORIA,
					SequenciaOrdenacao = sequenciaOrdenacao + 1,
				};

				categoriasImportacao.Add(categoriaImportacao);
			}

			await _importacaoCadastroIntegracaoRepository.InsertRange(categoriasImportacao);
		}

		public async Task EnviarCategoriasComProdutos(
			IEnumerable<CategoriaImportacaoCadastroViewModel> categoriasViewModel,
			Guid lojaId,
			Guid integracaoId)
		{
			await _importacaoCadastroIntegracaoRepository.LimparCategoriasEProdutosSelecionados(integracaoId);

			List<ImportacaoCadastroIntegracao> importacaoCadastros = new();

			importacaoCadastros.AddRange(
				categoriasViewModel
				.Where(categoria => categoria.Selecionado)
				.Select(categoria =>
					new ImportacaoCadastroIntegracao
					{
						CategoriaProdutoId = categoria.Id,
						LojaId = lojaId,
						IntegracaoId = integracaoId,
						TipoCadastro = TipoCadastroImportacaoIntegracao.CATEGORIA,
						SequenciaOrdenacao = categoria.SequenciaOrdenacao,
					}));

			foreach (var categoria in categoriasViewModel.Where(categoria => categoria.Selecionado))
			{
				importacaoCadastros.AddRange(
					categoria.Produtos
						.Where(produto => produto.Selecionado)
						.Select(produto =>
							new ImportacaoCadastroIntegracao
							{
								CategoriaProdutoId = categoria.Id,
								ProdutoId = produto.Id,
								LojaId = lojaId,
								IntegracaoId = integracaoId,
								TipoCadastro = TipoCadastroImportacaoIntegracao.PRODUTO,
								SequenciaOrdenacao = produto.SequenciaOrdenacao,
							}));
			}

			await _importacaoCadastroIntegracaoRepository.InsertRange(importacaoCadastros);
		}

		public async Task LimparRegistros(Guid integracaoId)
		{
			await _importacaoCadastroIntegracaoRepository.Limpar(integracaoId);
		}
	}
}
