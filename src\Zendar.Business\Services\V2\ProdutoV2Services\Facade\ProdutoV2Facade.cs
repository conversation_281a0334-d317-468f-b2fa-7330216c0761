﻿using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.AutoMappers.ProdutoMapper;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.IntegracaoServices.CaixaMovel.IntegracaoCaixaMovelProdutoService;
using Zendar.Business.Services.PesquisaProdutoServices.Popular;
using Zendar.Business.Services.V2.FichaTecnicaV2Services.AdicionarProdutoFichaTecnicaV2Service;
using Zendar.Business.Services.V2.ProdutoEtapaV2Services.AdicionarEtapaProdutoV2Service;
using Zendar.Business.Services.V1.ImportacaoCadastroIntegracaoService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCampoPersonalizadoService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorImagemService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorTamanhoEstoqueService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorTamanhoKitService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoCorTamanhoServices.ProdutoCorTamanhoService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoEcommerceService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoPrecosService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoService;
using Zendar.Business.Services.V2.ProdutoV2Services.ProdutoVariacaoFacade;
using Zendar.Business.Services.V2.ProdutoV2Services.TagProdutoService;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.TabelaPrecoProdutoCorTamanhoV2Service;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.TabelaPrecoProdutoV2Service;
using Zendar.Business.ViewModels.V2.FichaTecnica;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;
using Zendar.Data.Interfaces;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services.V2.ProdutoV2Services.Facade
{
    public class ProdutoV2Facade : BaseService, IProdutoV2Facade
    {
        private readonly IDatabaseTransaction _databaseTransaction;

        private readonly IPopularPesquisaProdutoService _popularPesquisaProdutoService;
        private readonly IIntegracaoCaixaMovelProdutoService _integracaoCaixaMovelProdutoService;
        private readonly IImportacaoCadastroIntegracaoService _importacaoCadastroIntegracaoService;

        private readonly IProdutoV2Service _produtoV2Service;
        private readonly IAdicionarProdutoFichaTecnicaV2Service _adicionarProdutoFichaTecnicaV2Service;
        private readonly IAdicionarEtapaProdutoV2Service _adicionarEtapaProdutoV2Service;
        private readonly IProdutoPrecoV2Service _produtoPrecoV2Service;
        private readonly IProdutoCampoPersonalizadoV2Service _produtoCampoPersonalizadoV2Service;
        private readonly IProdutoEcommerceV2Service _produtoEcommerceV2Service;
        private readonly IProdutoVariacaoV2Facade _produtoVariacaoV2Service;
        private readonly IProdutoCorV2Service _produtoCorV2Service;
        private readonly IProdutoCorTamanhoV2Service _produtoCorTamanhoV2Service;
        private readonly IProdutoCorTamanhoKitV2Service _produtoCorTamanhoKitV2Service;
        private readonly IProdutoCorImagemV2Service _produtoCorImagemV2Service;
        private readonly IProdutoCorTamanhoEstoqueV2Serivce _produtoCorTamanhoEstoqueV2Serivce;

        private readonly ITagProdutoV2Service _tagProdutoV2Service;

        private readonly ITabelaPrecoProdutoV2Service _tabelaPrecoProdutoV2Service;
        private readonly ITabelaPrecoProdutoCorTamanhoV2Service _tabelaPrecoProdutoCorTamanhoV2Service;

        private readonly ITamanhoService _tamanhoService;

        private readonly IProdutoRepository _produtoRepository;
        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;
        private readonly IProdutoCorTamanhoKitRepository _produtoCorTamanhoKitRepository;
        private readonly ILojaRepository _lojaRepository;

        public ProdutoV2Facade(
            INotificador notificador,
            IDatabaseTransaction databaseTransaction,
            IPopularPesquisaProdutoService popularPesquisaProdutoService,
            IIntegracaoCaixaMovelProdutoService integracaoCaixaMovelProdutoService,
			IImportacaoCadastroIntegracaoService importacaoCadastroIntegracaoService,
			IProdutoV2Service produtoV2Service,
            IProdutoPrecoV2Service produtoPrecoV2Service,
            IProdutoCampoPersonalizadoV2Service produtoCampoPersonalizadoV2Service,
            IProdutoEcommerceV2Service produtoEcommerceV2Service,
            IProdutoVariacaoV2Facade produtoVariacaoV2Service,
            IProdutoCorV2Service produtoCorV2Service,
            IProdutoCorTamanhoV2Service produtoCorTamanhoV2Service,
            IProdutoCorTamanhoKitV2Service produtoCorTamanhoKitV2Service,
            IProdutoCorImagemV2Service produtoCorImagemV2Service,
            IProdutoCorTamanhoEstoqueV2Serivce produtoCorTamanhoEstoqueV2Serivce,
            ITagProdutoV2Service tagProdutoV2Service,
            ITabelaPrecoProdutoV2Service tabelaPrecoProdutoV2Service,
            ITabelaPrecoProdutoCorTamanhoV2Service tabelaPrecoProdutoCorTamanhoV2Service,
            ITamanhoService tamanhoService,
            IProdutoRepository produtoRepository,
            IProdutoCorTamanhoRepository produtoCorTamanhoRepository,
            IProdutoCorTamanhoKitRepository produtoCorTamanhoKitRepository,
            ILojaRepository lojaRepository,
            IAdicionarEtapaProdutoV2Service adicionarEtapaProdutoV2Service,
            IAdicionarProdutoFichaTecnicaV2Service adicionarProdutoFichaTecnicaV2Service)
            : base(notificador)
        {
            _databaseTransaction = databaseTransaction;
            _popularPesquisaProdutoService = popularPesquisaProdutoService;
            _integracaoCaixaMovelProdutoService = integracaoCaixaMovelProdutoService;
			_importacaoCadastroIntegracaoService = importacaoCadastroIntegracaoService;
			_produtoV2Service = produtoV2Service;
            _produtoPrecoV2Service = produtoPrecoV2Service;
            _produtoCampoPersonalizadoV2Service = produtoCampoPersonalizadoV2Service;
            _produtoEcommerceV2Service = produtoEcommerceV2Service;
            _produtoVariacaoV2Service = produtoVariacaoV2Service;
            _produtoCorV2Service = produtoCorV2Service;
            _produtoCorTamanhoV2Service = produtoCorTamanhoV2Service;
            _produtoCorTamanhoKitV2Service = produtoCorTamanhoKitV2Service;
            _produtoCorImagemV2Service = produtoCorImagemV2Service;
            _produtoCorTamanhoEstoqueV2Serivce = produtoCorTamanhoEstoqueV2Serivce;
            _tagProdutoV2Service = tagProdutoV2Service;
            _tabelaPrecoProdutoV2Service = tabelaPrecoProdutoV2Service;
            _tabelaPrecoProdutoCorTamanhoV2Service = tabelaPrecoProdutoCorTamanhoV2Service;
            _tamanhoService = tamanhoService;
            _produtoRepository = produtoRepository;
            _produtoCorTamanhoRepository = produtoCorTamanhoRepository;
            _produtoCorTamanhoKitRepository = produtoCorTamanhoKitRepository;
            _lojaRepository = lojaRepository;
            _adicionarEtapaProdutoV2Service = adicionarEtapaProdutoV2Service;
            _adicionarProdutoFichaTecnicaV2Service = adicionarProdutoFichaTecnicaV2Service;
        }

		public void Dispose()
        {
            _produtoV2Service?.Dispose();
            _produtoPrecoV2Service?.Dispose();
            _produtoCampoPersonalizadoV2Service?.Dispose();
            _produtoEcommerceV2Service?.Dispose();
            _produtoVariacaoV2Service?.Dispose();
            _produtoCorV2Service?.Dispose();
            _produtoCorTamanhoV2Service?.Dispose();
            _produtoCorTamanhoKitV2Service?.Dispose();
            _produtoCorImagemV2Service?.Dispose();
            _produtoCorTamanhoEstoqueV2Serivce?.Dispose();

            _tabelaPrecoProdutoV2Service?.Dispose();
            _tabelaPrecoProdutoCorTamanhoV2Service?.Dispose();

            _tagProdutoV2Service?.Dispose();

            _integracaoCaixaMovelProdutoService?.Dispose();
            _popularPesquisaProdutoService?.Dispose();
			_importacaoCadastroIntegracaoService?.Dispose();


            _produtoRepository?.Dispose();
            _produtoCorTamanhoRepository?.Dispose();
            _produtoCorTamanhoKitRepository?.Dispose();
            _lojaRepository?.Dispose();
        }

        public async Task<Guid> CadastrarCompleto(ProdutoV2ViewModel produto, bool executarValidacao, bool withTransaction = true)
        {
            bool sucesso = true;
            Guid produtoId = Guid.Empty;

            try
            {
				if (withTransaction)
					_databaseTransaction.BeginTransaction();

				produtoId = await _produtoV2Service.Cadastrar(produto, executarValidacao);

				if (produtoId == Guid.Empty && produtoId == default)
					return default;

				#region [Ficha Técnica]
				if (produto.FichaTecnicaItens.Any())
				{
					produto.FichaTecnicaItens = produto.FichaTecnicaItens
						.Select(ft =>
						{
							ft.Id = Guid.NewGuid();
							ft.ProdutoPrincipalId = produtoId;
							ft.ProdutoTamanhosFichaTecnica = ft.ProdutoTamanhosFichaTecnica
								.Select(ptft =>
								{
									ptft.ProdutoFichaTecnicaId = ft.Id;
									ptft.ProdutoPrincipalId = produtoId;
									return ptft;
								})
								.ToArray();
							return ft;
						})
						.ToArray();

					foreach (var fichaTecnica in produto.FichaTecnicaItens)
					{
						var args = new CadastrarAlterarProdutoFichaTecnicaViewModel
						{
							MateriaPrimaFinal = fichaTecnica.MateriaPrimaFinal,
							ProdutoItemId = (Guid)fichaTecnica.ProdutoItem.Id,
							ProdutoPrincipalId = produtoId,
							Tamanhos = fichaTecnica.ProdutoTamanhosFichaTecnica
								.Select(ptft => new IdQuantidadeViewModel(
									id: ptft.Tamanho.Id,
									quantidade: ptft.Quantidade
								))
								.ToArray()
						};

						sucesso = await _adicionarProdutoFichaTecnicaV2Service.Adicionar(args);
						if (!sucesso) return default;
					}
				}
				#endregion

				#region [Etapas]
				if (produto.Etapas.Any())
				{
					produto.Etapas = produto.Etapas
						.Select(e =>
						{
							e.Id = Guid.NewGuid();
							e.ProdutoId = produtoId;
							return e;
						})
						.OrderBy(e => e.SequenciaOrdenacao) // necessário ordenar por sequência para manter a ordem na hora de inserir as etapas
						.ToArray();

					foreach (var etapa in produto.Etapas)
					{
						sucesso = await _adicionarEtapaProdutoV2Service.Adicionar(etapa) != Guid.Empty;
						if (!sucesso) return default;
					}
				}
				#endregion

				#region [Preços]
				if (produto.Precos != null)
				{
					produto.Precos.ProdutoId = produtoId;

					if (produto.Precos.ProdutoPrecoLojas == null)
					{
						NotificarAviso("Nenhum preço foi informado para as lojas.");
						return default;
					}

					// ProdutoPrecoLoja
					foreach (var produtoPrecoLoja in produto.Precos.ProdutoPrecoLojas)
						produtoPrecoLoja.ProdutoId = produtoId;
					sucesso = await _produtoPrecoV2Service.AtualizarPrecos(produto.Precos.ProdutoPrecoLojas);

					if (!sucesso) return default;

					if (produto.Precos.TabelaPrecoProdutos != null)
					{
						// TabelaPrecoProduto
						foreach (var tabelaPrecoProduto in produto.Precos.TabelaPrecoProdutos)
							tabelaPrecoProduto.ProdutoId = produtoId;
						await _tabelaPrecoProdutoV2Service.Cadastrar(produto.Precos.TabelaPrecoProdutos);

						if (!sucesso) return default;
					}
				}
				#endregion

				#region [E-commerces]
				if (produto.ProdutoEcommerces != null)
				{
					foreach (var produtoEcommerce in produto.ProdutoEcommerces)
					{
						produtoEcommerce.ProdutoId = produtoId;
						sucesso = await _produtoEcommerceV2Service.Cadastrar(produtoEcommerce);

						if (!sucesso) return default;
					}
					if (!sucesso) return default;
				}
				#endregion

				#region [Variação Padrão]
				if (produto.InformacoesVariacaoPadrao != null)
				{
					produto.InformacoesVariacaoPadrao.ProdutoId = produtoId;
					sucesso = await _produtoVariacaoV2Service.CadastrarVariacaoPadrao(produto.InformacoesVariacaoPadrao);
					if (!sucesso) return default;
				}
				#endregion

				#region [Campos personalizados]
				if (produto.CamposPersonalizados != null)
				{
					for (var campoPersonalizado = 0; campoPersonalizado < produto.CamposPersonalizados.Length; campoPersonalizado++)
						produto.CamposPersonalizados[campoPersonalizado].ProdutoId = produtoId;

					sucesso = await _produtoCampoPersonalizadoV2Service.Cadastrar(produto.CamposPersonalizados);
					if (!sucesso) return default;
				}
				#endregion

				#region Tags
				if (produto.Tags != null)
				{
					await _tagProdutoV2Service.Cadastrar(produtoId, produto.Tags);
				}
				#endregion

				if (produto.TipoProduto.Equals(TipoProduto.PRODUTO_VARIACAO))
				{
					var tamanhosProduto = produto?.ProdutoCores?
						.SelectMany(x => x.ProdutoCorTamanhos?
							.Select(y => y.TamanhoId))
						.Distinct();

					if (produto.TipoProduto == TipoProduto.PRODUTO_VARIACAO &&
					   !tamanhosProduto.Any())
					{
						var listaTamanhoPadrao = new List<Guid>();

						var tamanhoPadrao = await _tamanhoService.ObterComPredicate(c => c.PadraoSistema);

						if (tamanhoPadrao != null)
						{
							listaTamanhoPadrao.Add(tamanhoPadrao.Id);

							tamanhosProduto = listaTamanhoPadrao;
						}
					}

					List<ProdutoCorTamanhoV2ViewModel> produtoCorTamanhosAdicionar = new();

					foreach (var produtoCor in produto?.ProdutoCores)
					{
						produtoCor.ProdutoId = produtoId;
						produtoCor.Id = await _produtoCorV2Service.Cadastrar(produtoCor);

						/* lista da junção de todos os tamanhos do produto
						 * com a lista de tamanhos informados para essa cor
						 * dessa forma, a lista de tamanhos será uniforma para
						 * todas as cores do produto
						*/
						var produtoCorTamanhos = tamanhosProduto
							.Join(
								produtoCor.ProdutoCorTamanhos,
								tamId => tamId,
								pct => pct.TamanhoId,
								(tamId, pct) => new ProdutoCorTamanhoV2ViewModel
								{
									ProdutoCorId = produtoCor.Id,
									TamanhoId = tamId,
									Ativo = true,
									EstoqueMinimo = pct?.EstoqueMinimo ?? 0,
									EstoqueAtual = pct?.EstoqueAtual ?? 0,
									Identificadores = pct != null
													? pct.Identificadores
													: new(),
									Caracteristicas = pct != null
													? pct.Caracteristicas
													: new(),
									TabelaPrecoProdutoCorTamanhos = pct?.TabelaPrecoProdutoCorTamanhos
								});


						if (produtoCorTamanhos.Any())
							produtoCorTamanhosAdicionar.AddRange(produtoCorTamanhos);

						if (produtoCor.ProdutoCorImagens.Any())
						{
							foreach (var imagem in produtoCor.ProdutoCorImagens)
								imagem.ProdutoCorId = produtoCor.Id;

							var listaProdutoCorImagem = await _produtoCorImagemV2Service.ListarPorCor(produtoCor.Id);

							foreach (var produtoCorImagem in listaProdutoCorImagem)
								await _produtoCorImagemV2Service.Excluir(produtoCorImagem.Id);

							await _produtoCorImagemV2Service.Cadastrar(produtoCor.ProdutoCorImagens);
						}

						if (!sucesso) return default;
					}

					// é cadastrado aqui para gerar apenas uma operação de entrada por produto
					if (produtoCorTamanhosAdicionar.Count > 0)
					{
						var ids = await _produtoCorTamanhoV2Service.Cadastrar(produtoCorTamanhosAdicionar.ToArray());
						sucesso = ids.Count() >= produtoCorTamanhosAdicionar.Count;
					}
				}
				else if (produto.TipoProduto.Equals(TipoProduto.PRODUTO_KIT))
				{
					foreach (var itemKit in produto?.InformacoesVariacaoPadrao?.ProdutoCorTamanhoKits)
						await _produtoCorTamanhoKitV2Service.Cadastrar(produtoId, itemKit);
				}

				if (!sucesso) return default;

				await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produtoId);

				await _integracaoCaixaMovelProdutoService.EnviarProduto(produtoId, produto.CategoriaProdutoId);

				if (await _importacaoCadastroIntegracaoService.ValidarSeCategoriaExiste(produto.CategoriaProdutoId))
					await _importacaoCadastroIntegracaoService.EnviarProduto(produtoId, produto.CategoriaProdutoId);

				if (withTransaction)
					_databaseTransaction.Commit();
			}
			catch
			{
				if (withTransaction)
					_databaseTransaction.Rollback();

                throw;
			}

			return produtoId;
        }

        public async Task<Guid> CadastrarSimplificado(ProdutoSimplificadoV2ViewModel produtoSimplificado, Guid lojaBaseId, bool executarValidacao)
        {
            bool sucesso = true;
            Guid produtoId = Guid.Empty;

            var lojas = await _lojaRepository.FindAllSelectAsNoTracking(x => new Loja
            {
                Id = x.Id,
                LojaFiscal = new LojaFiscal { RegraFiscalPadraoId = x.LojaFiscal.RegraFiscalPadraoId }
            });

            // Preencher a regra fiscal com a regra fiscal padrão da loja base
            produtoSimplificado.RegraFiscalId = lojas
                .First(x => x.Id == lojaBaseId)
                .LojaFiscal
                .RegraFiscalPadraoId;

            // preencher os preços do produto
            produtoSimplificado.ProdutoPrecoLojas = lojas
                .Select(l => new ProdutoPrecoLojaV2ViewModel
                {
                    ProdutoId = produtoId,
                    LojaId = l.Id,
                    PrecoCompra = produtoSimplificado.PrecoCompra,
                    PrecoCusto = produtoSimplificado.PrecoCusto,
                    PrecoVenda = produtoSimplificado.PrecoVenda
                })
                .ToArray();

            ProdutoV2ViewModel produto = produtoSimplificado.ToProdutoViewModel();

            _databaseTransaction.BeginTransaction();

            #region [Produto]
            produtoId = await _produtoV2Service.Cadastrar(produto, executarValidacao);

            if (produtoId == Guid.Empty && produtoId == default)
                return default;
            #endregion

            #region [Preços]
            if (!sucesso) return default;

            foreach (var produtoPrecoLoja in produto.Precos.ProdutoPrecoLojas)
                produtoPrecoLoja.ProdutoId = produtoId;

            sucesso = await _produtoPrecoV2Service.AtualizarPrecos(produto.Precos.ProdutoPrecoLojas);
            #endregion

            #region [Variação Padrão]
            if (!sucesso) return default;

            produto.InformacoesVariacaoPadrao.ProdutoId = produtoId;
            sucesso = await _produtoVariacaoV2Service.CadastrarVariacaoPadrao(produto.InformacoesVariacaoPadrao);
            #endregion

            #region [Tags]
            if (!sucesso) return default;

            await _tagProdutoV2Service.Cadastrar(produtoId, produto.Tags);
            #endregion

            if (!sucesso) return default;

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produtoId);
            await _integracaoCaixaMovelProdutoService.EnviarProduto(produtoId, produto.CategoriaProdutoId);

			if (await _importacaoCadastroIntegracaoService.ValidarSeCategoriaExiste(produto.CategoriaProdutoId))
				await _importacaoCadastroIntegracaoService.EnviarProduto(produtoId, produto.CategoriaProdutoId);

			_databaseTransaction.Commit();
            return produtoId;
        }

        public async Task AlterarCompleto(ProdutoV2ViewModel produtoVm, bool executarValidacao)
        {
            bool sucesso = true;

            var produto = await _produtoV2Service.Obter(produtoVm.Id);
            if (produto == null) return;

            _databaseTransaction.BeginTransaction();

            #region [Produto]

            #region [Dados Gerais]
            produto.CategoriaProdutoId = produtoVm.CategoriaProdutoId;
            produto.MarcaId = produtoVm.MarcaId;
            produto.UnidadeMedidaId = produtoVm.UnidadeMedidaId;
            produto.Nome = produtoVm.Nome;
            produto.NomeAbreviado = produtoVm.NomeAbreviado;
            produto.Referencia = produtoVm.Referencia;
            produto.VideoUrl = produtoVm.VideoUrl;
            produto.VenderEcommerce = produtoVm.VenderEcommerce;
            #endregion

            #region [Informações Fiscais]
            produto.InformacoesFiscais.RegraFiscalId = produtoVm.InformacoesFiscais.RegraFiscalId;
            produto.InformacoesFiscais.IcmsStRetidoBaseCalculo = produtoVm.InformacoesFiscais.IcmsStRetidoBaseCalculo;
            produto.InformacoesFiscais.IcmsStRetidoValor = produtoVm.InformacoesFiscais.IcmsStRetidoValor;
            produto.InformacoesFiscais.FcpStRetidoBaseCalculo = produtoVm.InformacoesFiscais.FcpStRetidoBaseCalculo;
            produto.InformacoesFiscais.FcpStRetidoValor = produtoVm.InformacoesFiscais.FcpStRetidoValor;

            produto.InformacoesFiscais.IcmsAliquota = produtoVm.InformacoesFiscais.IcmsAliquota;
            produto.InformacoesFiscais.PisAliquota = produtoVm.InformacoesFiscais.PisAliquota;
            produto.InformacoesFiscais.CofinsAliquota = produtoVm.InformacoesFiscais.CofinsAliquota;
            produto.InformacoesFiscais.FcpAliquota = produtoVm.InformacoesFiscais.FcpAliquota;
            produto.InformacoesFiscais.IcmsReducaoBaseCalculo = produtoVm.InformacoesFiscais.IcmsReducaoBaseCalculo;
            produto.InformacoesFiscais.CodigoBeneficioFiscal = produtoVm.InformacoesFiscais.CodigoBeneficioFiscal;

            produto.InformacoesFiscais.UnidadeTributavelId = produtoVm.InformacoesFiscais.UnidadeTributavelId;
            produto.InformacoesFiscais.QtdeConversao = produtoVm.InformacoesFiscais.QtdeConversao;
            produto.InformacoesFiscais.FatorConversao = produtoVm.InformacoesFiscais.FatorConversao;

            produto.InformacoesFiscais.CNPJFabricante = produtoVm.InformacoesFiscais.CNPJFabricante;
            produto.InformacoesFiscais.IndicadorEscalaRelevante = produtoVm.InformacoesFiscais.IndicadorEscalaRelevante;

            produto.InformacoesFiscais.CodigoAnp = produtoVm.InformacoesFiscais.CodigoAnp;
            produto.InformacoesFiscais.CODIF = produtoVm.InformacoesFiscais.CODIF;
            produto.InformacoesFiscais.PercentualGLP = produtoVm.InformacoesFiscais.PercentualGLP;
            produto.InformacoesFiscais.PercentualGasNacional = produtoVm.InformacoesFiscais.PercentualGasNacional;
            produto.InformacoesFiscais.PercentualGasImportado = produtoVm.InformacoesFiscais.PercentualGasImportado;
            produto.InformacoesFiscais.ValorPartidaGLP = produtoVm.InformacoesFiscais.ValorPartidaGLP;

            produto.InformacoesFiscais.CodigoNcm = produtoVm.InformacoesFiscais.CodigoNcm;
            produto.InformacoesFiscais.CodigoCest = produtoVm.InformacoesFiscais.CodigoCest;

            produto.InformacoesFiscais.CstOrigem = produtoVm.InformacoesFiscais.CstOrigem;
            produto.InformacoesFiscais.TipoProdutoFiscal = produtoVm.InformacoesFiscais.TipoProdutoFiscal;
            #endregion

            #region [Informações Adicionais]
            produto.InformacoesAdicionais.ControlaEstoque = produtoVm.InformacoesAdicionais.ControlaEstoque;
            produto.InformacoesAdicionais.PermiteAlteraValorNaVenda = produtoVm.InformacoesAdicionais.PermiteAlteraValorNaVenda;
            produto.InformacoesAdicionais.SolicitarInformacaoComplementarNoPdv = produtoVm.InformacoesAdicionais.SolicitarInformacaoComplementarNoPdv;
            produto.InformacoesAdicionais.UtilizarBalanca = produtoVm.InformacoesAdicionais.UtilizarBalanca;
            produto.InformacoesAdicionais.ExportarBalanca = produtoVm.InformacoesAdicionais.ExportarBalanca;
            #endregion

            await _produtoV2Service.Alterar(produtoVm, executarValidacao);
            #endregion

            #region [Preços]
            produtoVm.Precos.ProdutoId = produtoVm.Id;

            // ProdutoPrecoLoja
            foreach (var produtoPrecoLoja in produtoVm.Precos.ProdutoPrecoLojas)
                produtoPrecoLoja.ProdutoId = produto.Id;
            sucesso = await _produtoPrecoV2Service.AtualizarPrecos(produtoVm.Precos.ProdutoPrecoLojas);

            if (!sucesso) return;

            // TabelaPrecoProduto
            foreach (var tabelaPrecoProduto in produtoVm.Precos.TabelaPrecoProdutos)
                tabelaPrecoProduto.ProdutoId = produto.Id;
            await _tabelaPrecoProdutoV2Service.AtualizarPrecos(produtoVm.Precos.TabelaPrecoProdutos);

            if (!sucesso) return;
            #endregion

            #region [E-commerces]
            foreach (var produtoEcommerce in produtoVm.ProdutoEcommerces)
                produtoEcommerce.ProdutoId = produto.Id;

            sucesso = await _produtoEcommerceV2Service.AtualizarProdutosEcommerces(produtoVm.ProdutoEcommerces);
            if (!sucesso) return;
            #endregion

            #region [Variação Padrão]
            if (produto.TipoProduto == TipoProduto.PRODUTO_SIMPLES &&
                produtoVm.InformacoesVariacaoPadrao != null)
            {
                var variacaoPadrao = await _produtoVariacaoV2Service.ObterVariacaoPadrao(produtoVm.Id);

                if (variacaoPadrao == null) return;

                variacaoPadrao.Identificadores.CodigoExterno = produtoVm.InformacoesVariacaoPadrao.CodigoExterno;
                variacaoPadrao.Identificadores.CodigoGTINEAN = produtoVm.InformacoesVariacaoPadrao.CodigoGTINEAN;
                variacaoPadrao.Caracteristicas.Altura = produtoVm.InformacoesVariacaoPadrao.OutrasInformacoesAltura;
                variacaoPadrao.Caracteristicas.Largura = produtoVm.InformacoesVariacaoPadrao.OutrasInformacoesLargura;
                variacaoPadrao.Caracteristicas.Profundidade = produtoVm.InformacoesVariacaoPadrao.OutrasInformacoesProfundidade;
                variacaoPadrao.Caracteristicas.PesoLiquido = produtoVm.InformacoesVariacaoPadrao.OutrasInformacoesPesoLiquido;
                variacaoPadrao.Caracteristicas.PesoBruto = produtoVm.InformacoesVariacaoPadrao.OutrasInformacoesPesoBruto;
                variacaoPadrao.Caracteristicas.PesoEmbalagem = produtoVm.InformacoesVariacaoPadrao.OutrasInformacoesPesoEmbalagem;

                await _produtoCorTamanhoV2Service.Alterar(produto.Id, variacaoPadrao);
            }
            #endregion

            #region [Campos personalizados]
            for (var campoPersonalizado = 0; campoPersonalizado < produtoVm.CamposPersonalizados.Length; campoPersonalizado++)
                produtoVm.CamposPersonalizados[campoPersonalizado].ProdutoId = produto.Id;

            sucesso = await _produtoCampoPersonalizadoV2Service.AtualizarValores(produtoVm.CamposPersonalizados);
            if (!sucesso) return;
            #endregion

            #region Tags
            await _tagProdutoV2Service.AtualizarTags(produto.Id, produtoVm.Tags);
            #endregion

            if (produto.TipoProduto.Equals(TipoProduto.PRODUTO_VARIACAO))
            {
                foreach (var produtoCor in produtoVm?.ProdutoCores)
                {
                    if (produtoCor.Id == Guid.Empty &&
                        produtoCor.CorId != Guid.Empty)
                    {
                        var produtoCorV2ViewModel = _produtoCorV2Service.ObterPorProdutoIdECorId(produto.Id, produtoCor.CorId);

                        if (produtoCorV2ViewModel != null)
                        {
                            produtoCor.Id = produtoCorV2ViewModel.Id;
                            produtoCor.ProdutoId = produtoCorV2ViewModel.ProdutoId;
                        }

                        foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                        {
                            if (produtoCorTamanho.Id == Guid.Empty &&
                                produtoCorTamanho.TamanhoId != Guid.Empty)
                            {
                                var produtoCorTamanhoV2ViewModel = _produtoCorTamanhoV2Service.ObterPorProdutoCorIdETamanhoId(produtoCor.Id, produtoCorTamanho.TamanhoId);

                                if (produtoCorTamanhoV2ViewModel != null)
                                {
                                    produtoCorTamanho.Id = produtoCorTamanhoV2ViewModel.Id;
                                    produtoCorTamanho.ProdutoCorId = produtoCorTamanhoV2ViewModel.ProdutoCorId;
                                }
                            }
                        }
                    }
                }

                await _produtoCorTamanhoV2Service.Alterar(produto.Id, produtoVm?.ProdutoCores?.SelectMany(pc => pc.ProdutoCorTamanhos).ToArray());
            }
            else if (produto.TipoProduto.Equals(TipoProduto.PRODUTO_KIT))
            {
                foreach (var itemKit in produtoVm?.InformacoesVariacaoPadrao?.ProdutoCorTamanhoKits)
                    await _produtoCorTamanhoKitV2Service.Alterar(itemKit, null);
            }

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produto.Id);

            _databaseTransaction.Commit();
        }

        public async Task<Guid> Duplicar(Guid produtoId, bool executarValidacao)
        {
            var produto = await _produtoRepository.ObterProdutoParaDuplicar(produtoId);

            if (produto == null)
            {
                NotificarAvisoRegistroNaoEncontrado("produto");
                return default;
            }

            var produtoVm = produto.ToViewModel();
            produtoVm.Nome = $"Cópia - {produtoVm.Nome}";
            produtoVm.Foto = string.Empty;
            produtoVm.InformacoesFood.ImagemCardapio = string.Empty;
            produtoVm.InformacoesFood.ImagemDestaque = string.Empty;

            foreach (var produtoEcommerce in produtoVm.ProdutoEcommerces)
            {
                produtoEcommerce.Titulo = string.Empty;
                produtoEcommerce.Anunciado = false;
            }

            if (produto.TipoProduto == TipoProduto.PRODUTO_KIT)
            {
                produtoVm.InformacoesVariacaoPadrao.ProdutoCorTamanhoKits = await ObterItensDoKitParaDuplicar(produto);
            }

            return await CadastrarCompleto(produtoVm, executarValidacao);
        }

        private async Task<ProdutoCorTamanhoKitV2ViewModel[]> ObterItensDoKitParaDuplicar(
            Produto produto)
        {
            // Id da variação padrão do produto kit
            var produtoCorTamanhoPrincipalId = produto
                    .ProdutoCores
                    .FirstOrDefault(x => x.Cor.PadraoSistema)
                    ?.ProdutoCorTamanhos
                    ?.FirstOrDefault(x => x.Tamanho.PadraoSistema)
                    ?.Id;

            var produtoCorTamanhoKits = await _produtoCorTamanhoKitRepository
                .FindAllSelectAsNoTracking(
                    pctki => pctki.ProdutoCorTamanhoPrincipalId == produtoCorTamanhoPrincipalId,
                    pctki => new ProdutoCorTamanhoKit
                    {
                        ProdutoCorTamanhoItemId = pctki.ProdutoCorTamanhoItemId,
                        Quantidade = pctki.Quantidade,
                        ProdutoCorTamanhoKitPrecoLojas = pctki.ProdutoCorTamanhoKitPrecoLojas.Select(ikl => new ProdutoCorTamanhoKitPrecoLoja
                        {
                            LojaId = ikl.LojaId,
                            Valor = ikl.Valor,
                        }).ToList()
                    });

            return produtoCorTamanhoKits
                .Select(pctki => pctki.ToViewModel())
                .ToArray();
        }

        public async Task AlterarDadosGerais(ProdutoSimplificadoV2ViewModel produtoVm, bool executarValidacao)
        {
            var produto = await _produtoV2Service.Obter(produtoVm.Id);
            if (produto == null) return;

            _databaseTransaction.BeginTransaction();

            #region [Dados Gerais]
            produto.Ativo = produtoVm.Ativo;
            produto.Nome = produtoVm.Nome;
            produto.NomeAbreviado = produtoVm.NomeAbreviado;
            produto.Referencia = produtoVm.Referencia;
            produto.Foto = produtoVm.Foto;
            produto.VenderEcommerce = produtoVm.VenderEcommerce;
            produto.CategoriaProdutoId = produtoVm.CategoriaProdutoId;
            produto.MarcaId = produtoVm.MarcaId;
            produto.UnidadeMedidaId = produtoVm.UnidadeMedidaId;
            produto.Tags = produtoVm.Tags;
            produto.TipoProduto = produtoVm.TipoProduto;

            await _produtoV2Service.Alterar(produto, executarValidacao);
            #endregion

            #region [Variação padrão]
            if (produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO)
            {
                if (PossuiAvisos()) return;

                var variacaoPadrao = await _produtoVariacaoV2Service.ObterVariacaoPadrao(produto.Id);

                if (variacaoPadrao == null) return;

                variacaoPadrao.EstoqueAtual = produtoVm.EstoqueAtual;
                variacaoPadrao.EstoqueMinimo = produtoVm.EstoqueMinimo;
                variacaoPadrao.Identificadores.CodigoGTINEAN = produtoVm.CodigoGTINEAN;

                await _produtoCorTamanhoV2Service.Alterar(produto.Id, variacaoPadrao);
            }
            #endregion

            #region [Tags]
            await _tagProdutoV2Service.AtualizarTags(produto.Id, produto.Tags);
            #endregion

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produto.Id);

            if (!PossuiAvisos() || !PossuiErros())
                _databaseTransaction.Commit();
        }

        public async Task AlterarInformacoesFiscais(ProdutoFiscaisV2ViewModel produtoFiscal, bool executarValidacao)
        {
            var produto = await _produtoV2Service.Obter(produtoFiscal.ProdutoId);
            if (produto == null) return;

            #region [InformaçõesFiscais]
            produto.InformacoesFiscais.RegraFiscalId = produtoFiscal.RegraFiscalId;
            produto.InformacoesFiscais.IcmsStRetidoBaseCalculo = produtoFiscal.IcmsStRetidoBaseCalculo;
            produto.InformacoesFiscais.IcmsStRetidoValor = produtoFiscal.IcmsStRetidoValor;
            produto.InformacoesFiscais.FcpStRetidoBaseCalculo = produtoFiscal.FcpStRetidoBaseCalculo;
            produto.InformacoesFiscais.FcpStRetidoValor = produtoFiscal.FcpStRetidoValor;

            produto.InformacoesFiscais.IcmsAliquota = produtoFiscal.IcmsAliquota;
            produto.InformacoesFiscais.PisAliquota = produtoFiscal.PisAliquota;
            produto.InformacoesFiscais.CofinsAliquota = produtoFiscal.CofinsAliquota;
            produto.InformacoesFiscais.FcpAliquota = produtoFiscal.FcpAliquota;
            produto.InformacoesFiscais.IcmsReducaoBaseCalculo = produtoFiscal.IcmsReducaoBaseCalculo;
            produto.InformacoesFiscais.CodigoBeneficioFiscal = produtoFiscal.CodigoBeneficioFiscal;

            produto.InformacoesFiscais.UnidadeTributavelId = produtoFiscal.UnidadeTributavelId;
            produto.InformacoesFiscais.QtdeConversao = produtoFiscal.QtdeConversao;
            produto.InformacoesFiscais.FatorConversao = produtoFiscal.FatorConversao;

            produto.InformacoesFiscais.CNPJFabricante = produtoFiscal.CNPJFabricante;
            produto.InformacoesFiscais.IndicadorEscalaRelevante = produtoFiscal.IndicadorEscalaRelevante;

            produto.InformacoesFiscais.CodigoAnp = produtoFiscal.CodigoAnp;
            produto.InformacoesFiscais.CODIF = produtoFiscal.CODIF;
            produto.InformacoesFiscais.PercentualGLP = produtoFiscal.PercentualGLP;
            produto.InformacoesFiscais.PercentualGasNacional = produtoFiscal.PercentualGasNacional;
            produto.InformacoesFiscais.PercentualGasImportado = produtoFiscal.PercentualGasImportado;
            produto.InformacoesFiscais.ValorPartidaGLP = produtoFiscal.ValorPartidaGLP;

            produto.InformacoesFiscais.CodigoNcm = produtoFiscal.CodigoNcm;
            produto.InformacoesFiscais.CodigoCest = produtoFiscal.CodigoCest;

            produto.InformacoesFiscais.CstOrigem = produtoFiscal.CstOrigem;
            produto.InformacoesFiscais.TipoProdutoFiscal = produtoFiscal.TipoProdutoFiscal;
            produto.InformacoesFiscais.ProdutoRegraFiscalExcecoes = produtoFiscal.ProdutoRegraFiscalExcecoes;
            produto.InformacoesFiscais.ProdutoOrigemCombustivel = produtoFiscal.ProdutoOrigemCombustivel;
            produto.InformacoesFiscais.AliquotaAdREmICMSRetido = produtoFiscal.AliquotaAdREmICMSRetido;
            produto.InformacoesFiscais.QuantidadeBCMonoRetido = produtoFiscal.QuantidadeBCMonoRetido;
            #endregion

            await _produtoV2Service.Alterar(produto, executarValidacao);
        }

        public async Task AlterarOutrasInformacoes(ProdutoOutrasInformacoesV2ViewModel outrasInformacoesProduto, bool executarValidacao)
        {
            var produto = await _produtoV2Service.Obter(outrasInformacoesProduto.ProdutoId);
            if (produto == null) return;

            if (outrasInformacoesProduto.CodigoIntegracao > 0 && 
                await _produtoRepository.Any(x => x.Id != outrasInformacoesProduto.ProdutoId && x.CodigoIntegracao == outrasInformacoesProduto.CodigoIntegracao))
            {
                NotificarAviso(ResourceMensagem.ProdutoService_JaCadastradoCodigoIntegracao);
                return;
            }
            _databaseTransaction.BeginTransaction();

            #region [Informações Adicionais]
            produto.InformacoesAdicionais.ControlaEstoque = outrasInformacoesProduto.InformacoesAdicionais.ControlaEstoque;
            produto.InformacoesAdicionais.UtilizarBalanca = outrasInformacoesProduto.InformacoesAdicionais.UtilizarBalanca;
            produto.InformacoesAdicionais.ExportarBalanca = outrasInformacoesProduto.InformacoesAdicionais.ExportarBalanca;
            produto.InformacoesAdicionais.PermiteAlteraValorNaVenda = outrasInformacoesProduto.InformacoesAdicionais.PermiteAlteraValorNaVenda;
            produto.InformacoesAdicionais.SolicitarInformacaoComplementarNoPdv = outrasInformacoesProduto.InformacoesAdicionais.SolicitarInformacaoComplementarNoPdv;
            produto.CodigoIntegracao = outrasInformacoesProduto.CodigoIntegracao;


            await _produtoV2Service.Alterar(produto, executarValidacao);
            #endregion

            #region [Variação Padrão]
            if (produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO)
            {
                var variacaoPadrao = await _produtoVariacaoV2Service.ObterVariacaoPadrao(produto.Id);

                if (variacaoPadrao == null) return;

                variacaoPadrao.Identificadores.CodigoExterno = outrasInformacoesProduto.CodigoExterno;
                variacaoPadrao.Caracteristicas.Altura = outrasInformacoesProduto.Caracteristicas.Altura;
                variacaoPadrao.Caracteristicas.Largura = outrasInformacoesProduto.Caracteristicas.Largura;
                variacaoPadrao.Caracteristicas.Profundidade = outrasInformacoesProduto.Caracteristicas.Profundidade;
                variacaoPadrao.Caracteristicas.PesoLiquido = outrasInformacoesProduto.Caracteristicas.PesoLiquido;
                variacaoPadrao.Caracteristicas.PesoBruto = outrasInformacoesProduto.Caracteristicas.PesoBruto;
                variacaoPadrao.Caracteristicas.PesoEmbalagem = outrasInformacoesProduto.Caracteristicas.PesoEmbalagem;

                await _produtoCorTamanhoV2Service.Alterar(produto.Id, variacaoPadrao);

                await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produto.Id);
            }
            #endregion

            #region [Campos Personalizados]
            await _produtoCampoPersonalizadoV2Service.AtualizarValores(outrasInformacoesProduto.CamposPersonalizados);
            #endregion

            _databaseTransaction.Commit();
        }

        public async Task AlterarInformacoesFood(ProdutoInformacoesFoodV2ViewModel informacoesFood, bool executarValidacao)
        {
            var produto = await _produtoV2Service.Obter(informacoesFood.ProdutoId);
            if (produto == null) return;

            produto.InformacoesAdicionais.UtilizarBalanca = informacoesFood.UtilizarBalanca;
            produto.InformacoesAdicionais.ExportarBalanca = informacoesFood.ExportarBalanca;
            produto.InformacoesFood.UsarComoComplemento = informacoesFood.UsarComoComplemento;
            produto.InformacoesFood.ProdutoCombo = informacoesFood.ProdutoCombo;
            produto.InformacoesFood.CobrarTaxaServico = informacoesFood.CobrarTaxaServico;
            produto.InformacoesFood.BaixarSaldoMateriaPrima = informacoesFood.BaixarSaldoMateriaPrima;
            produto.InformacoesFood.UtilizarPrecoDosItensEtapa = informacoesFood.UtilizarPrecoDosItensEtapa;
            produto.InformacoesFood.ComposicaoProduto = informacoesFood.ComposicaoProduto;
            produto.InformacoesFood.DiasParaValidade = informacoesFood.DiasParaValidade;
            produto.InformacoesFood.PrecoCombo = informacoesFood.PrecoCombo;
            produto.InformacoesFood.ImagemCardapio = informacoesFood.ImagemCardapio;
            produto.InformacoesFood.ImagemDestaque = informacoesFood.ImagemDestaque;
            produto.InformacoesFood.GerenciadorImpressaoId = informacoesFood.GerenciadorImpressaoId;
            produto.InformacoesFood.GerenciadorImpressao = informacoesFood.GerenciadorImpressao;

            await _produtoV2Service.Alterar(produto, executarValidacao);
        }

        public async Task<ProdutoV2ViewModel> ObterDadosGerais(Guid id, Guid lojaId)
        {
            var produto = await _produtoV2Service.Obter(id);

            if (produto == null ||
                produto?.TipoProduto == TipoProduto.PRODUTO_VARIACAO)
            {
                return produto;
            }

            var variacaoPadrao = await _produtoVariacaoV2Service.ObterVariacaoPadrao(id);

            if (variacaoPadrao != null)
            {
                produto.InformacoesVariacaoPadrao = new ProdutoVariacaoPadraoV2ViewModel(produto.Id, variacaoPadrao);

                if (produto?.TipoProduto == TipoProduto.PRODUTO_SIMPLES)
                    produto.InformacoesVariacaoPadrao.EstoqueAtual = await _produtoCorTamanhoEstoqueV2Serivce.ObterEstoqueAtual(variacaoPadrao.Id, lojaId);
            }

            return produto;
        }

        public async Task<ProdutoOutrasInformacoesV2ViewModel> ObterOutrasInformacoes(Guid produtoId)
        {
            var produtoInformacoesAdicionais = await _produtoV2Service.ObterInformacoesAdicionais(produtoId);
            if (produtoInformacoesAdicionais == null) return null;

            var variacaoPadrao = await _produtoVariacaoV2Service.ObterVariacaoPadrao(produtoId);
            var camposPersonalizados = await _produtoCampoPersonalizadoV2Service.ListarPorProduto(produtoId);

            return new ProdutoOutrasInformacoesV2ViewModel()
            {
                ProdutoId = produtoId,
                InformacoesAdicionais = produtoInformacoesAdicionais.InformacoesAdicionais,
                CodigoExterno = variacaoPadrao?.Identificadores?.CodigoExterno,
                CodigoBarrasInterno = variacaoPadrao?.Identificadores?.CodigoBarrasInterno,
                Caracteristicas = variacaoPadrao?.Caracteristicas,
                CamposPersonalizados = camposPersonalizados?.ToArray(),
                CodigoIntegracao = produtoInformacoesAdicionais.CodigoIntegracao.Value
            };
        }
    }
}
