﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Business.ViewModels.Integracao.IntegracaoSnapshot;
using Zendar.Business.ViewModels.V2.ProdutoViewModels;

namespace Zendar.Business.Services.IntegracaoServices.ProdutoPlataformaService
{
    public interface IProdutoPlataformaService
    {
        Task<ProdutoV2ViewModel> CadastrarAlterarProduto(
            ProdutoSnapshotViewModel produtoSnapshotViewModel,
            int identificacaoIntegracao,
            Guid lojaId,
            Guid? tabelaPrecoId,
            Guid tabelaPrecoPadraoId,
            string url,
			bool withTransaction = true);

        Task<Guid?> CadastrarAlterarMarca(
            EntidadeSnapshotViewModel marca,
            int identificacaoIntegracao,
            string url);

        Task<Guid?> CadastrarAlterarCor(
            EntidadeSnapshotViewModel cor,
            int identificacaoIntegracao,
            string url);

        Task<Guid?> CadastrarAlterarTamanho(
            EntidadeSnapshotViewModel tamanho,
            int identificacaoIntegracao,
            string url);

        Task<Guid?> CadastrarAlterarListaCategoria(
            List<CategoriaSnapshotViewModel> listaCategoria,
            int identificacaoIntegracao,
            string url);
    }
}