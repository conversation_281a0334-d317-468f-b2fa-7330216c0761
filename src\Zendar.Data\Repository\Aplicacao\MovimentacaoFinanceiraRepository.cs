﻿using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.ViewModels;
using ZendarPackage.NotaFiscal.Enums;

namespace Zendar.Data.Repository.Aplicacao
{
    public class MovimentacaoFinanceiraRepository : RepositoryAplicacao<MovimentacaoFinanceira>, IMovimentacaoFinanceiraRepository
    {
        public MovimentacaoFinanceiraRepository(AplicacaoContexto context) : base(context) { }

        public Task<DateTime> ConsultarVencimentoContaEmAbertoMaisAntiga(Guid clienteId)
        {
            return FiltrarContasEmAbertoPorCliente(clienteId)
                        .OrderBy(c => c.DataVencimento)
                        .Select(c => c.DataVencimento)
                        .FirstOrDefaultAsync();
        }

        public Task<List<MovimentacaoFinanceira>> ObterContasEmAberto(Guid clienteId)
        {
            return FiltrarContasEmAbertoPorCliente(clienteId)
                    .Select(m => new MovimentacaoFinanceira
                    {
                        Valor = m.Valor,
                        DataVencimento = m.DataVencimento,
                        MovimentacoesFinanceirasBaixa = m.MovimentacoesFinanceirasBaixa.Select(b => new MovimentacaoFinanceiraBaixa
                        {
                            Valor = b.Valor,
                            Desconto = b.Desconto,
                            Multa = b.Multa,
                            Juros = b.Juros
                        }).ToList()
                    })
                    .ToListAsync();
        }

        private IQueryable<MovimentacaoFinanceira> FiltrarContasEmAbertoPorCliente(Guid clienteId)
        {
            return DbSet.Where(c => c.Operacao.ClienteFornecedorId == clienteId &&
                                    c.Operacao.Status == StatusOperacao.EFETUADA &&
                                    c.Operacao.TipoOperacao.AcaoFinanceira == TipoAcao.ENTRADA &&
                                    c.Operacao.Loja.LojaServicos.Any(s => s.TipoServico == TipoServicoStargate.PLANO && s.DataBloqueio.Date >= DateTime.UtcNow.Date) &&
                                    c.Valor > c.MovimentacoesFinanceirasBaixa.Sum(b => b.Valor + b.Desconto - b.Multa - b.Juros));
        }

        public Task<List<MovimentacaoFinanceira>> ObterParaExcluir(Guid? identificadorAgrupamento, Guid? operacaoId)
        {
            if (!identificadorAgrupamento.HasValue && !operacaoId.HasValue)
            {
                return null;
            }

            var query = DbSet.AsQueryable();

            if (identificadorAgrupamento.HasValue)
            {
                query = query.Where(m => m.IdentificadorAgrupamento == identificadorAgrupamento);
            }
            else if (operacaoId.HasValue)
            {
                query = query.Where(m => m.OperacaoId == operacaoId);
            }

            return query.Include(m => m.MovimentacoesFinanceirasBaixa).ThenInclude(b => b.ValeMovimentacao)
                        .Include(m => m.FormaPagamentoRecebimento)
                        .Include(m => m.Operacao).ThenInclude(o => o.TipoOperacao)
                        .ToListAsync();
        }

        public async Task<List<MovimentacaoFinanceira>> ObterParaExcluirContasPagarBaixas(IEnumerable<Guid> movimentacaoFinanceiraId)
        {
            //Obter uma lista de movimentacoes do tipo contas pagar
            return await DbSet.Where(x => movimentacaoFinanceiraId.Contains(x.Id) &&
                                     x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.CONTAS_PAGAR)
                              .Include(x => x.MovimentacoesFinanceirasBaixa)
                              .ToListAsync();
        }

        public async Task<List<MovimentacaoFinanceira>> ObterParaExcluirContasPagar(IEnumerable<Guid> operacaoId)
        {
            //Obter uma lista de movimentacoes do tipo contas pagar e sem movimentacao baixa
            return await DbSet.Where(x => operacaoId.Any(o => o.Equals(x.OperacaoId)) &&
                                     x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.CONTAS_PAGAR &&
                                     !x.MovimentacoesFinanceirasBaixa.Any())
                              .Include(x => x.Operacao)
                              .Include(x => x.MovimentacoesFinanceirasBaixa)
                              .ToListAsync();
        }

        public async Task<bool> VerificarExistenciaContasEmAbertoCliente(Guid id)
        {
            return await DbSet.AnyAsync(x => x.Operacao.ClienteFornecedor.Id.Equals(id) &&
                          (x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.CONTAS_RECEBER ||
                          x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA) &&
                          x.Operacao.ClienteFornecedor.TipoCadastroPessoa == TipoCadastroPessoa.CLIENTE &&
                          x.Valor > x.MovimentacoesFinanceirasBaixa.Sum(b => b.Valor + b.Desconto - b.Multa - b.Juros));
        }

        public Task<List<MovimentacaoFinanceira>> ObterParcelas(Guid identificadorAgrupamento)
        {
            return DbSet.Where(m => m.IdentificadorAgrupamento == identificadorAgrupamento)
                .Select(m => new MovimentacaoFinanceira
                {
                    Id = m.Id,
                    NumeroParcela = m.NumeroParcela,
                    DataVencimento = m.DataVencimento,
                    Valor = m.Valor
                }).ToListAsync();
        }

        public async Task<MovimentacaoFinanceira> ObterParaLancarMovimentacaoBaixa(Guid movimentacaoFinanceiraId)
        {
            return await DbSet.Where(m => m.Id == movimentacaoFinanceiraId)
                        .AsNoTracking()
                        .Select(m => new MovimentacaoFinanceira
                        {
                            Valor = m.Valor - m.MovimentacoesFinanceirasBaixa.Sum(b => b.Valor - b.Multa - b.Juros + b.Desconto),
                            DataVencimento = m.DataVencimento
                        })
                        .FirstOrDefaultAsync();
        }

        public GridPaginadaRetorno<MovimentacaoFinanceira> ListarMovimentacoesPaginadoPorCaixaMovimentacao(GridPaginadaConsulta gridPaginada, Guid id)
        {
            var gridPaginadaRetorno = new GridPaginadaRetorno<MovimentacaoFinanceira>();

            var queryOperacao = DbSet.Where(x => (x.Operacao.CaixaMovimentacaoId.Equals(id) || x.MovimentacoesFinanceirasBaixa.Any(b => b.CaixaMovimentacaoId.Equals(id)))
                                                && x.Operacao.TipoOperacao.AcaoFinanceira != TipoAcao.SEM_ACAO
                                                && !(x.Operacao.OperacaoTroca != null && x.FormaPagamentoRecebimento.MeioPagamento.MeioDePagamentoFiscal == MeioPagamentoFiscal.VALE_PRESENTE && x.Operacao.Id == x.Operacao.OperacaoTroca.TrocaId));

            Expression<Func<MovimentacaoFinanceira, MovimentacaoFinanceira>> selector = m => new MovimentacaoFinanceira
            {
                ParcelaExibicao = m.ParcelaExibicao,
                Valor = m.Valor,
                FormaPagamentoRecebimento = new FormaPagamentoRecebimento { Nome = m.FormaPagamentoRecebimento.Nome },
                Operacao = new Operacao
                {
                    DataEmissao = m.Operacao.DataEmissao,
                    NumeroOperacao = m.Operacao.NumeroOperacao,
                    Status = m.Operacao.Status,
                    Observacao = m.Operacao.Observacao,
                    TipoOperacao = new TipoOperacao
                    {
                        IdentificacaoTipoOperacao = m.Operacao.TipoOperacao.IdentificacaoTipoOperacao,
                        AcaoFinanceira = m.Operacao.TipoOperacao.AcaoFinanceira
                    },
                    ClienteFornecedor = m.Operacao.ClienteFornecedorId.HasValue
                    ? new ClienteFornecedor
                    {
                        Nome = m.Operacao.ClienteFornecedor.Nome
                    }
                    : null
                },
                MovimentacoesFinanceirasBaixa = m.MovimentacoesFinanceirasBaixa.Select(b => new MovimentacaoFinanceiraBaixa
                {
                    CaixaMovimentacaoId = b.CaixaMovimentacaoId,
                    Valor = b.Valor,
                    FormaPagamentoRecebimento = new FormaPagamentoRecebimento { Nome = b.FormaPagamentoRecebimento.Nome }

                }).ToList()
            };

            var query = gridPaginada.DirecaoOrdenacao == "asc"
                ? queryOperacao.OrderBy(x => x.Operacao.DataEmissao).Select(selector)
                : queryOperacao.OrderByDescending(x => x.Operacao.DataEmissao).Select(selector);


            gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);

            return gridPaginadaRetorno;
        }

        public async Task<List<ControleCaixaMovimentacoesPaginadaPorCaixaMovimentacaoViewModel>> ListarMovimentacoesPorCaixaMovimentacao(Guid id, bool? filtrarQuantidade = null)
        {
            var query = DbSet.Where(x => x.Operacao.CaixaMovimentacaoId.Equals(id))
                                     .Select(o => new ControleCaixaMovimentacoesPaginadaPorCaixaMovimentacaoViewModel
                                     {
                                         DataEmissao = o.Operacao.DataEmissao,
                                         IdentificacaoTipoOperacao = o.Operacao.TipoOperacao.IdentificacaoTipoOperacao,
                                         NumeroOperacao = o.Operacao.NumeroOperacao,
                                         FormaPagamentoRecebimentoNome = o.FormaPagamentoRecebimento.Nome,
                                         Parcela = o.ParcelaExibicao,
                                         Valor = o.Operacao.TipoOperacao.AcaoFinanceira.Equals(TipoAcao.SAIDA) ? (o.Valor * -1) : o.Valor,
                                         AcaoFinanceira = o.Operacao.TipoOperacao.AcaoFinanceira,
                                         Cancelada = o.Operacao.Status == StatusOperacao.CANCELADA ? true : false,
                                         Observacao = o.Operacao.Observacao
                                     });
            if (filtrarQuantidade.HasValue && filtrarQuantidade.Value)
            {
                query.Take(10);
            }

            return await query.ToListAsync();
        }

        public GridPaginadaRetornoContasPagar<ContasPagarPaginadoViewModel> ListarMovimentacoesContasPagar(GridPaginadaConsulta gridPaginada, ContasPagarFiltroViewModel contasPagarFiltroViewModel, Guid lojaId, bool? isRelatorio = false)
        {
            var gridPaginadaRetorno = new GridPaginadaRetornoContasPagar<ContasPagarPaginadoViewModel>();

            //Aplica o filtro que vem na ContasPagarFiltroViewModel
            var queryWhere = FiltrarMovimentacoesContasPagar(contasPagarFiltroViewModel, lojaId);

            var totalMultasJuros = queryWhere.Select(x => x.MovimentacoesFinanceirasBaixa.Sum(y => y.Multa + y.Juros)).ToList().Sum();
            var totalDescontos = queryWhere.Select(x => x.MovimentacoesFinanceirasBaixa.Sum(y => y.Desconto)).ToList().Sum();
            var valor = queryWhere.Select(x => x.Valor).ToList().Sum();
            var totalAPagar = totalMultasJuros - totalDescontos + valor;

            gridPaginadaRetorno.TotalMultasJuros = totalMultasJuros;
            gridPaginadaRetorno.TotalDescontos = totalDescontos;
            gridPaginadaRetorno.TotalAPagar = totalAPagar;

            //Consulta e montagem da lista
            var query = queryWhere.OrderBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                            .Select(mf => new ContasPagarPaginadoViewModel
                            {
                                EntradaMercadoriaId = mf.Operacao.EntradaMercadoria != null ? mf.Operacao.EntradaMercadoria.Id : null,
                                DataVencimento = mf.DataVencimento,
                                FaturaId = mf.Operacao.Fatura != null ? mf.Operacao.Fatura.Id : null,
                                FormaPagamento = mf.FormaPagamentoRecebimento.Nome,
                                FornecedorNomeFantasia = mf.Operacao.ClienteFornecedor.Apelido,
                                FornecedorRazaoSocial = mf.Operacao.ClienteFornecedor.Nome,
                                Telefone = mf.Operacao.ClienteFornecedor.Telefone,
                                Celular = mf.Operacao.ClienteFornecedor.Celular,
                                PlanoContas = mf.PlanoConta.Nome,
                                MovimentacaoFinanceiraId = mf.Id,
                                OperacaoId = mf.OperacaoId,
                                DataEmissaoOperacao = mf.Operacao.DataEmissao,
                                Parcela = mf.ParcelaExibicao,
                                TipoOperacao = mf.Operacao.TipoOperacao.IdentificacaoTipoOperacao,
                                Valor = mf.Valor,
                                PossuiBaixasEmOutrasParcelas = DbSet
                                    .Where(x => x.OperacaoId == mf.OperacaoId
                                             && x.Id != mf.Id
                                             && x.MovimentacoesFinanceirasBaixa.Any()).Any(),
                                MovimentacaoFinanceiraBaixa = mf.MovimentacoesFinanceirasBaixa.Select(mfb => new ContasPagarReceberMovimentacaoBaixaViewModel
                                {
                                    DataPagamento = mfb.DataPagamento,
                                    Desconto = mfb.Desconto,
                                    Juros = mfb.Juros,
                                    Multa = mfb.Multa,
                                }).AsEnumerable()
                            });

            //Realiza a paginação
            if (!isRelatorio.Value)
            {
                gridPaginadaRetorno.CarregarPaginacao(query, gridPaginada);
            }
            else
            {
                gridPaginadaRetorno.Registros = query.ToList();
                gridPaginadaRetorno.Total = query.Count();
            }

            return gridPaginadaRetorno;
        }

        public async Task<ContasPagarReceberDetalhesViewModel> ObterDetalhes(Guid movimentacaoFinanceiraId)
        {
            return await DbSet.Where(x => x.Id == movimentacaoFinanceiraId)
                .Select(x => new ContasPagarReceberDetalhesViewModel
                {
                    DataVencimento = x.DataVencimento,
                    FornecedorNomeFantasia = x.Operacao.ClienteFornecedor.Apelido,
                    FornecedorRazaoSocial = x.Operacao.ClienteFornecedor.Nome,
                    PlanoContas = x.PlanoConta.Nome,
                    OperacaoId = x.Operacao.EntradaMercadoria != null ? x.Operacao.EntradaMercadoria.Id : x.OperacaoId,
                    Parcela = x.ParcelaExibicao,
                    TipoOperacao = x.Operacao.TipoOperacao.IdentificacaoTipoOperacao,
                    Valor = x.Valor,
                    Competencia = x.Operacao.DataCompetencia,
                    DataEmissao = x.Operacao.DataEmissao,
                    Historico = x.Operacao.Historico,
                    Identificador = x.Operacao.NumeroOperacao,
                    NumeroDocumento = x.Operacao.NumeroDocumento,
                    NumeroNFe = x.Operacao.EntradaMercadoria.NumeroNFe,
                    MovimentacaoFinanceiraBaixa = x.MovimentacoesFinanceirasBaixa.Select(v => new ContasPagarReceberMovimentacaoBaixaDetalhesViewModel
                    {
                        DataPagamento = v.DataPagamento,
                        Desconto = v.Desconto,
                        Valor = v.Valor + v.Desconto - v.Juros - v.Multa,
                        MultaJuros = v.Juros + v.Multa,
                        NomeUsuario = v.Usuario.Nome,
                        FormaPagamento = v.FormaPagamentoRecebimento.Nome,
                        ContaFinanceira = v.ContaFinanceira.Nome
                    }).AsEnumerable()
                }).FirstOrDefaultAsync();
        }

        private IQueryable<MovimentacaoFinanceira> FiltrarMovimentacoesContasPagar(ContasPagarFiltroViewModel contasPagarFiltroViewModel, Guid lojaId)
        {
            //Pega as contas a pagar
            var queryWhere = DbSet.Where(x => (x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.CONTAS_PAGAR ||
                                               x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.PAGAMENTO_COMPRA ||
                                               x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.COMPRA_MERCADORIA) &&
                                               x.Operacao.TipoOperacao.AcaoFinanceira != TipoAcao.ENTRADA &&
                                               x.Operacao.Status == StatusOperacao.EFETUADA &&
                                               x.Operacao.LojaId == lojaId);

            //Data de Vencimento
            if (contasPagarFiltroViewModel.DataVencimentoInicio != null)
                queryWhere = queryWhere.Where(x => x.DataVencimento >= contasPagarFiltroViewModel.DataVencimentoInicio && x.DataVencimento <= contasPagarFiltroViewModel.DataVencimentoFim);

            //Data de Emissão
            if (contasPagarFiltroViewModel.DataEmissaoInicio != null)
                queryWhere = queryWhere.Where(x => x.Operacao.DataEmissao >= contasPagarFiltroViewModel.DataEmissaoInicio && x.Operacao.DataEmissao <= contasPagarFiltroViewModel.DataEmissaoFim);

            //Data de Pagamento
            if (contasPagarFiltroViewModel.DataPagamentoInicio != null)
                queryWhere = queryWhere.Where(x => x.MovimentacoesFinanceirasBaixa.Any(b => b.DataPagamento >= contasPagarFiltroViewModel.DataPagamentoInicio && b.DataPagamento <= contasPagarFiltroViewModel.DataPagamentoFim));

            //Plano de Contas
            if (contasPagarFiltroViewModel.PlanoContasId != null)
                queryWhere = queryWhere.Where(x => x.PlanoContaId == contasPagarFiltroViewModel.PlanoContasId);

            bool pesquisaRelacionadaComFatura = contasPagarFiltroViewModel.ExibirFatura || contasPagarFiltroViewModel.ExibirItensFatura;
            if (!pesquisaRelacionadaComFatura)
            {
                queryWhere = queryWhere.Where(x => x.Operacao.Fatura == null && !x.MovimentacoesFinanceirasBaixa.Any(y => y.FaturaId.HasValue));
            }
            else
            {
                // Exibição de fatura e itens da fatura
                if (contasPagarFiltroViewModel.ExibirFatura && contasPagarFiltroViewModel.ExibirItensFatura)
                    queryWhere = queryWhere.Where(x => x.Operacao.Fatura != null || x.MovimentacoesFinanceirasBaixa.Any(y => y.FaturaId.HasValue));
                else
                {
                    //Exibir só Fatura
                    if (contasPagarFiltroViewModel.ExibirFatura)
                        queryWhere = queryWhere.Where(x => x.Operacao.Fatura != null);

                    //Exibir apenas itens Fatura
                    if (contasPagarFiltroViewModel.ExibirItensFatura)
                        queryWhere = queryWhere.Where(x => x.MovimentacoesFinanceirasBaixa.Any(y => y.FaturaId.HasValue));
                }
            }

            //Numero de Documento
            if (contasPagarFiltroViewModel.NumeroDocumento != null)
            {
                queryWhere = queryWhere.Where(x => x.Operacao.NumeroDocumento == contasPagarFiltroViewModel.NumeroDocumento ||
                                                   x.Operacao.EntradaMercadoria.NumeroNFe.Contains(contasPagarFiltroViewModel.NumeroDocumento));
            }

            //Identificador/Numero Operacao
            if (contasPagarFiltroViewModel.Identificador > 0)
                queryWhere = queryWhere.Where(x => x.Operacao.NumeroOperacao == contasPagarFiltroViewModel.Identificador);

            //Data da Competencia
            if (contasPagarFiltroViewModel.Competencia != null)
                queryWhere = queryWhere.Where(x => x.Operacao.DataCompetencia.Month == contasPagarFiltroViewModel.Competencia.Value.Month &&
                                                   x.Operacao.DataCompetencia.Year == contasPagarFiltroViewModel.Competencia.Value.Year);

            //Forma de Pagamento Emissão
            if (contasPagarFiltroViewModel.FormaPagamentoEmissaoId != null)
                queryWhere = queryWhere.Where(x => x.FormaPagamentoRecebimentoId.Equals(contasPagarFiltroViewModel.FormaPagamentoEmissaoId));

            //Forma de Pagamento Baixa
            if (contasPagarFiltroViewModel.FormaPagamentoBaixaId != null)
                queryWhere = queryWhere.Where(x => x.MovimentacoesFinanceirasBaixa.Any(c => c.FormaPagamentoRecebimentoId.Equals(contasPagarFiltroViewModel.FormaPagamentoBaixaId)));

            //Cliente Fornecedor
            if (contasPagarFiltroViewModel.ClienteFornecedorId != null)
                queryWhere = queryWhere.Where(x => x.Operacao.ClienteFornecedorId == contasPagarFiltroViewModel.ClienteFornecedorId);

            //Histórico
            if (!string.IsNullOrEmpty(contasPagarFiltroViewModel.Historico))
                queryWhere = queryWhere.Where(x => EF.Functions.Collate(x.Operacao.Historico, Collates.RemoverCaracteresEspeciaisAcentos).Contains(contasPagarFiltroViewModel.Historico));

            //Situacao da conta a pagar
            if (contasPagarFiltroViewModel.Situacao != SituacaoBaixa.TODAS)
            {
                if (contasPagarFiltroViewModel.Situacao == SituacaoBaixa.CONTA_BAIXADA)
                {
                    queryWhere = queryWhere.Where(x => x.MovimentacoesFinanceirasBaixa.Any());
                }
                else
                {
                    queryWhere = queryWhere.Where(x => !x.MovimentacoesFinanceirasBaixa.Any());
                }
            }

            return queryWhere;
        }

        public GridPaginadaRetornoContasReceber<ContasReceberPaginadoViewModel> ListarMovimentacoesContasReceber(GridPaginadaConsulta gridPaginada, ContasReceberFiltroViewModel contasReceberFiltroViewModel, Guid lojaId)
        {
            var gridPaginadaRetorno = new GridPaginadaRetornoContasReceber<ContasReceberPaginadoViewModel>();

            //Aplica o filtro que vem na ContasPagarFiltroViewModel
            var queryWhere = FiltrarMovimentacoesContasReceber(contasReceberFiltroViewModel, lojaId);

            var filtro = MontarFiltroMovimentacaoBaixa(contasReceberFiltroViewModel);

            //Consulta e montagem da lista
            var query = queryWhere.OrderBy($"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}")
                            .Select(mf => new ContasReceberPaginadoViewModel
                            {
                                MovimentacaoFinanceiraId = mf.Id,
                                OperacaoId = mf.OperacaoId,
                                DataVencimento = mf.DataVencimento,
                                DataEmissao = mf.Operacao.DataEmissao,
                                Parcela = mf.ParcelaExibicao,
                                ClienteFornecedorRazaoSocial = mf.Operacao.ClienteFornecedor.Nome,
                                ClienteFornecedorNome = mf.Operacao.ClienteFornecedor.Apelido,
                                ClienteFornecedorId = mf.Operacao.ClienteFornecedorId,
                                Telefone = mf.Operacao.ClienteFornecedor.Telefone,
                                Celular = mf.Operacao.ClienteFornecedor.Celular,
                                FormaRecebimento = mf.FormaPagamentoRecebimento.Nome,
                                PlanoContas = mf.PlanoConta.Nome,
                                ValorOriginal = mf.Valor,
                                TipoOperacao = mf.Operacao.TipoOperacao.IdentificacaoTipoOperacao,
                                NumeroOperacao = mf.Operacao.NumeroOperacao,
                                PossuiBaixasEmOutrasParcelas = DbSet
                                    .Where(x => x.OperacaoId == mf.OperacaoId
                                             && x.Id != mf.Id
                                             && x.MovimentacoesFinanceirasBaixa.Any()).Any(),
                                MovimentacaoFinanceiraBaixa = mf.MovimentacoesFinanceirasBaixa
                                .AsQueryable()
                                .Where(filtro)
                                .Select(mfb => new ContasPagarReceberMovimentacaoBaixaViewModel
                                {
                                    Id = mfb.Id,
                                    DataPagamento = mfb.DataPagamento,
                                    Desconto = mfb.Desconto,
                                    Juros = mfb.Juros,
                                    Multa = mfb.Multa,
                                    Valor = mfb.Valor,
                                    FormaRecebimentoNome = mfb.FormaPagamentoRecebimento.Nome,
                                    FormaPagamentoRecebimentoId = mfb.FormaPagamentoRecebimentoId
                                })
                                .ToList(),
                            });

            //Realiza a paginação
            gridPaginadaRetorno.CarregarPaginacao(query);

            return gridPaginadaRetorno;
        }

        private IQueryable<MovimentacaoFinanceira> FiltrarMovimentacoesContasReceber(ContasReceberFiltroViewModel contasReceberFiltroViewModel, Guid lojaId)
        {
            //Pega as contas a receber e vendas
            var queryWhere = DbSet.Where(x => (x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.CONTAS_RECEBER ||
                                         x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA) &&
                                         x.Operacao.Status == StatusOperacao.EFETUADA &&
                                         x.Operacao.LojaId.Equals(lojaId));

            //Cliente Fornecedor
            if (contasReceberFiltroViewModel.ClienteFornecedorId != null)
                queryWhere = queryWhere.Where(x => x.Operacao.ClienteFornecedorId == contasReceberFiltroViewModel.ClienteFornecedorId);

            //Data de Vencimento
            if (contasReceberFiltroViewModel.DataVencimentoInicio.HasValue)
                queryWhere = queryWhere.Where(x => x.DataVencimento >= contasReceberFiltroViewModel.DataVencimentoInicio && x.DataVencimento <= contasReceberFiltroViewModel.DataVencimentoFim);

            //Data de Emissão
            if (contasReceberFiltroViewModel.DataEmissaoInicio != null && contasReceberFiltroViewModel.DataEmissaoFim != null)
                queryWhere = queryWhere.Where(x => x.Operacao.DataEmissao >= contasReceberFiltroViewModel.DataEmissaoInicio && x.Operacao.DataEmissao <= contasReceberFiltroViewModel.DataEmissaoFim);

            //Data de Pagamento
            if (contasReceberFiltroViewModel.DataPagamentoInicio != null && contasReceberFiltroViewModel.DataPagamentoFim != null)
                queryWhere = queryWhere.Where(x => x.MovimentacoesFinanceirasBaixa.Any(y => y.DataPagamento >= contasReceberFiltroViewModel.DataPagamentoInicio && y.DataPagamento <= contasReceberFiltroViewModel.DataPagamentoFim));

            //Data de Compensacao
            if (contasReceberFiltroViewModel.DataCompensacaoInicio != null && contasReceberFiltroViewModel.DataCompensacaoFim != null)
                queryWhere = queryWhere.Where(x => x.MovimentacoesFinanceirasBaixa.Any(y => y.DataCompensacao >= contasReceberFiltroViewModel.DataCompensacaoInicio && y.DataCompensacao <= contasReceberFiltroViewModel.DataCompensacaoFim));

            //Plano de Contas
            if (contasReceberFiltroViewModel.PlanoContasId != null)
                queryWhere = queryWhere.Where(x => x.PlanoContaId == contasReceberFiltroViewModel.PlanoContasId);

            //Forma de Pagamento Emissao
            if (contasReceberFiltroViewModel.FormaPagamentoEmissaoId != null)
                queryWhere = queryWhere.Where(x => x.FormaPagamentoRecebimentoId.Equals(contasReceberFiltroViewModel.FormaPagamentoEmissaoId));

            //Forma de Pagamento Baixa
            if (contasReceberFiltroViewModel.FormaPagamentoBaixaId != null)
                queryWhere = queryWhere.Where(x => x.MovimentacoesFinanceirasBaixa.Any(c => c.FormaPagamentoRecebimentoId.Equals(contasReceberFiltroViewModel.FormaPagamentoBaixaId)));

            //Situacao da conta a receber
            if (contasReceberFiltroViewModel.Situacao != SituacaoBaixa.TODAS)
            {
                if (contasReceberFiltroViewModel.Situacao == SituacaoBaixa.CONTA_BAIXADA)
                {
                    queryWhere = queryWhere.Where(x => x.MovimentacoesFinanceirasBaixa.Any());
                }
                else
                {
                    queryWhere = queryWhere.Where(x => !x.MovimentacoesFinanceirasBaixa.Any() ||
                                                        x.Valor > x.MovimentacoesFinanceirasBaixa.Sum(b => b.Valor + b.Desconto - b.Multa - b.Juros));
                }
            }

            //Numero de Documento
            if (contasReceberFiltroViewModel.NumeroDocumento != null)
                queryWhere = queryWhere.Where(x => x.Operacao.NumeroDocumento == contasReceberFiltroViewModel.NumeroDocumento);

            //Identificador/Numero Operacao
            if (contasReceberFiltroViewModel.Identificador > 0)
                queryWhere = queryWhere.Where(x => x.Operacao.NumeroOperacao == contasReceberFiltroViewModel.Identificador);

            //Histórico
            if (!string.IsNullOrEmpty(contasReceberFiltroViewModel.Historico))
                queryWhere = queryWhere.Where(x => EF.Functions.Collate(x.Operacao.Historico, Collates.RemoverCaracteresEspeciaisAcentos).Contains(contasReceberFiltroViewModel.Historico));

            return queryWhere;
        }

        public decimal ObterValorContasEmAtraso(Guid clienteFornecedorId)
        {
            var queryContasEmAberto = FiltrarContasEmAbertoPorCliente(clienteFornecedorId);

            return queryContasEmAberto.Where(m => m.DataVencimento.Date < DateTime.UtcNow.Date)
                                      .Select(m => new MovimentacaoFinanceira
                                      {
                                          Valor = m.Valor,
                                          MovimentacoesFinanceirasBaixa = m.MovimentacoesFinanceirasBaixa.Select(b => new MovimentacaoFinanceiraBaixa
                                          {
                                              Valor = b.Valor,
                                              Desconto = b.Desconto,
                                              Multa = b.Multa,
                                              Juros = b.Juros
                                          }).ToList()
                                      })
                                      .ToList()
                                      .Sum(m => m.Valor - m.MovimentacoesFinanceirasBaixa.Sum(c => c.Valor - c.Desconto + c.Multa + c.Juros));

        }

        public decimal ObterValorContasEmAberto(Guid clienteFornecedorId)
        {
            return FiltrarContasEmAbertoPorCliente(clienteFornecedorId)
                    .Select(m => new MovimentacaoFinanceira
                    {
                        Valor = m.Valor,
                        DataVencimento = m.DataVencimento,
                        MovimentacoesFinanceirasBaixa = m.MovimentacoesFinanceirasBaixa.Select(b => new MovimentacaoFinanceiraBaixa
                        {
                            Valor = b.Valor,
                            Desconto = b.Desconto,
                            Multa = b.Multa,
                            Juros = b.Juros
                        }).ToList()
                    })
                    .ToList()
                    .Sum(m => m.Valor - m.MovimentacoesFinanceirasBaixa.Sum(c => c.Valor - c.Desconto + c.Multa + c.Juros));
        }

        public async Task<ContasReceberDetalhesViewModel> ObterDetalhesContaReceber(Guid movimentacaoFinanceiraId)
        {
            return await DbSet.Where(x => x.Id == movimentacaoFinanceiraId)
                .Select(x => new ContasReceberDetalhesViewModel
                {
                    DataVencimento = x.DataVencimento,
                    ClienteFornecedorRazaoSocial = x.Operacao.ClienteFornecedor.Nome,
                    ClienteFornecedorNomeFantasia = x.Operacao.ClienteFornecedor.Apelido,
                    ValorRecebido = x.MovimentacoesFinanceirasBaixa.Sum(y => y.Valor),
                    ValorOriginal = x.Valor,
                    Parcela = x.ParcelaExibicao,
                    DataEmissao = x.Operacao.DataEmissao,
                    PlanoContas = x.PlanoConta.Nome,
                    Competencia = x.Operacao.DataCompetencia,
                    DataPagamento = x.MovimentacoesFinanceirasBaixa.Count > 0 ? x.MovimentacoesFinanceirasBaixa.First().DataPagamento : null,
                    NumeroDocumento = x.Operacao.NumeroDocumento,
                    NumeroNotaFiscal = x.Operacao.DocumentoFiscal.Count > 0 ? x.Operacao.DocumentoFiscal.First().Numero : null,
                    ModeloNotaFiscal = x.Operacao.DocumentoFiscal.Count > 0 ? x.Operacao.DocumentoFiscal.First().ModeloFiscal : null,
                    Identificador = x.Operacao.NumeroOperacao,
                    TipoOperacao = x.Operacao.TipoOperacao.IdentificacaoTipoOperacao,
                    OperacaoId = x.OperacaoId,
                    Historico = x.Operacao.Historico,
                    Operador = x.Operacao.Usuario.Nome,
                    MovimentacaoFinanceiraBaixa = x.MovimentacoesFinanceirasBaixa.Select(v => new ContasReceberMovimentacaoBaixaDetalhesViewModel
                    {
                        DataPagamento = v.DataPagamento,
                        UsuarioBaixa = v.Usuario.Nome,
                        FormaPagamento = v.FormaPagamentoRecebimento.Nome,
                        Valor = v.Valor - v.Juros - v.Multa + v.Desconto,
                        MultaJuros = v.Juros + v.Multa,
                        Desconto = v.Desconto,
                        Compensacao = v.DataCompensacao,
                        TotalRecebido = v.Valor,
                        TaxaTransacao = v.TaxaTransacao,
                        ContaFinanceira = v.ContaFinanceira.Nome
                    }).AsEnumerable()
                }).FirstOrDefaultAsync();
        }

        public async Task<MovimentacaoFinanceira> ObterParaBaixar(Guid movimentacaoId)
        {
            return await DbSet.AsNoTracking()
                              .Where(x => x.Id.Equals(movimentacaoId))
                              .Select(x => new MovimentacaoFinanceira
                              {
                                  Id = x.Id,
                                  Valor = x.Valor,
                                  NumeroParcela = x.NumeroParcela,
                                  IdentificadorAgrupamento = x.IdentificadorAgrupamento,
                                  DataVencimento = x.DataVencimento,
                                  ValorParaComissao = x.ValorParaComissao,
                                  PlanoContaId = x.PlanoContaId,
                                  OperacaoId = x.OperacaoId,
                                  Operacao = new Operacao
                                  {
                                      DataHoraUsuario = x.Operacao.DataHoraUsuario,
                                      DataEmissao = x.Operacao.DataEmissao,
                                      CaixaMovimentacaoId = x.Operacao.CaixaMovimentacaoId,
                                      ValorTotal = x.Operacao.ValorTotal,
                                      TipoOperacao = new TipoOperacao
                                      {
                                          AcaoFinanceira = x.Operacao.TipoOperacao.AcaoFinanceira,
                                          IdentificacaoTipoOperacao = x.Operacao.TipoOperacao.IdentificacaoTipoOperacao
                                      },
                                      LojaId = x.Operacao.LojaId,
                                      VendedorId = x.Operacao.VendedorId,
                                      OperacaoTroca = x.Operacao.OperacaoTroca != null
                                                    ? new OperacaoTroca
                                                    {
                                                        TrocaId = x.Operacao.OperacaoTroca.TrocaId
                                                    }
                                                    : null,
                                      Fatura = x.Operacao.Fatura != null
                                             ? new Fatura { Id = x.Operacao.Fatura.Id }
                                             : null
                                  },
                                  MovimentacoesFinanceirasBaixa = x.MovimentacoesFinanceirasBaixa
                                  .Select(y => new MovimentacaoFinanceiraBaixa
                                  {
                                      Valor = y.Valor,
                                      Multa = y.Multa,
                                      Juros = y.Juros,
                                      Desconto = y.Desconto,
                                  })
                                  .ToList()
                              })
                              .FirstOrDefaultAsync();
        }

        public async Task<List<MovimentacaoFinanceira>> ObterMovimentacoesDeOperacaoParaBaixar(Guid operacaoId)
        {
            return await DbSet.AsNoTracking()
                              .Where(x => x.OperacaoId.Equals(operacaoId)
                                       && x.MovimentacoesFinanceirasBaixa.Sum(b => b.Valor) < x.Valor)
                              .Select(x => new MovimentacaoFinanceira
                              {
                                  Id = x.Id,
                                  IdentificadorAgrupamento = x.IdentificadorAgrupamento,
                                  FormaPagamentoRecebimentoId = x.FormaPagamentoRecebimentoId,
                                  FormaPagamentoRecebimentoParcelaId = x.FormaPagamentoRecebimentoParcelaId,
                                  Valor = x.Valor,
                                  Troco = x.Troco,
                                  DataVencimento = x.DataVencimento,
                                  Operacao = new Operacao
                                  {
                                      DataEmissao = x.Operacao.DataEmissao,
                                      CaixaMovimentacaoId = x.Operacao.CaixaMovimentacaoId
                                  }
                              }).ToListAsync();
        }

        public async Task<MovimentacaoFinanceira> ObterMovimentacaoParaCancelarBaixa(Guid movimentacaoFinanceiraId)
        {
            return await DbSet.AsNoTracking().Include(x => x.MovimentacoesFinanceirasBaixa).FirstAsync(x => x.Id.Equals(movimentacaoFinanceiraId));
        }

        public async Task<MovimentacaoFinanceira> ObterInformacoesDoCabecalhoDoRecibo(Guid movimentacaoFinanceiraId)
        {
            return await DbSet.AsNoTracking().Where(m => m.Id == movimentacaoFinanceiraId).Select(m => new MovimentacaoFinanceira
            {
                Operacao = new Operacao
                {
                    Loja = new Loja
                    {
                        Fantasia = m.Operacao.Loja.Fantasia,
                        RazaoSocial = m.Operacao.Loja.RazaoSocial,
                        CpfCnpj = m.Operacao.Loja.CpfCnpj,
                        Logradouro = m.Operacao.Loja.Logradouro,
                        Numero = m.Operacao.Loja.Numero,
                        Bairro = m.Operacao.Loja.Bairro,
                        Cep = m.Operacao.Loja.Cep,
                        Telefone = m.Operacao.Loja.Telefone,
                        Celular = m.Operacao.Loja.Celular,
                        Email = m.Operacao.Loja.Email,
                        LojaImpressaoRelatorio = new LojaImpressaoRelatorio
                        {
                            LogoQuadrado = m.Operacao.Loja.LojaImpressaoRelatorio.LogoQuadrado,
                        },
                        Cidade = new Cidade { CidadeUf = m.Operacao.Loja.Cidade.CidadeUf }
                    }
                }
            }).FirstOrDefaultAsync();
        }

        public async Task<List<ContasReceberPaginadoViewModel>> ObterContasReceberParaTotalizadoresDaListaPaginada(ContasReceberFiltroViewModel contasReceberFiltroViewModel, Guid lojaId)
        {
            //Aplica o filtro que vem na ContasPagarFiltroViewModel
            var queryWhere = FiltrarMovimentacoesContasReceber(contasReceberFiltroViewModel, lojaId);

            var filtro = MontarFiltroMovimentacaoBaixa(contasReceberFiltroViewModel);

            //Consulta e montagem da lista
            return await queryWhere.Select(x => new ContasReceberPaginadoViewModel
            {
                DataVencimento = x.DataVencimento,
                ValorOriginal = x.Valor,
                MovimentacaoFinanceiraBaixa = x.MovimentacoesFinanceirasBaixa
                .AsQueryable()
                .Where(filtro)
                .Select(v => new ContasPagarReceberMovimentacaoBaixaViewModel
                {
                    FormaPagamentoRecebimentoId = v.FormaPagamentoRecebimentoId,
                    DataPagamento = v.DataPagamento,
                    Desconto = v.Desconto,
                    Juros = v.Juros,
                    Multa = v.Multa,
                    Valor = v.Valor
                })
                .ToList()
            }).ToListAsync();
        }
        private Expression<Func<MovimentacaoFinanceiraBaixa, bool>> MontarFiltroMovimentacaoBaixa(ContasReceberFiltroViewModel contasReceberFiltroViewModel)
        {
            return s => (contasReceberFiltroViewModel.FormaPagamentoBaixaId == null ||
                            s.FormaPagamentoRecebimentoId == contasReceberFiltroViewModel.FormaPagamentoBaixaId) &&
                            (
                                contasReceberFiltroViewModel.DataPagamentoInicio == null ||
                                s.DataPagamento.Date == contasReceberFiltroViewModel.DataPagamentoInicio.Value.Date ||
                                (s.DataPagamento >= contasReceberFiltroViewModel.DataPagamentoInicio
                                && s.DataPagamento <= contasReceberFiltroViewModel.DataPagamentoFim)
                            );
        }

        public async Task<List<MovimentacaoFinanceira>> ObterInformacoesRecebimentosCancelados(IEnumerable<Guid> movimentacoesFinanceriasId)
        {
            return await DbSet.Where(x => movimentacoesFinanceriasId.Contains(x.Id))
                              .Select(x => new MovimentacaoFinanceira
                              {
                                  Id = x.Id,
                                  OperacaoId = x.OperacaoId,
                                  Operacao = new Operacao
                                  {
                                      LojaId = x.Operacao.LojaId,
                                      VendedorId = x.Operacao.VendedorId
                                  }
                              })
                              .ToListAsync();
        }

        public async Task<List<Guid>> ObterOperacoesParaCalcularValorParaComissao(Guid lojaId, DateTime periodo)
        {
            return await DbSet.Where(m => m.Operacao.LojaId == lojaId &&
                                          m.Operacao.Status == StatusOperacao.EFETUADA &&
                                          (m.Operacao.DataEmissao >= periodo || m.MovimentacoesFinanceirasBaixa.Any(b => b.DataPagamento >= periodo)) &&
                                          //m.ValorParaComissao == 0m && (manter comentário)
                                          (m.Operacao.TipoOperacao.IdentificacaoTipoOperacao.Equals(IdentificacaoTipoOperacao.VENDA) ||
                                          m.Operacao.TipoOperacao.IdentificacaoTipoOperacao.Equals(IdentificacaoTipoOperacao.DEVOLUCAO)))
                              .Select(m => m.OperacaoId)
                              .ToListAsync();
        }

        public async Task<bool> VerificarSeOperacaoPossuiDevolucaoPorMovimentacaoFinanceira(List<Guid> movimentacoesFinaceiras)
        {
            return await DbSet.AnyAsync(x => movimentacoesFinaceiras.Contains(x.Id) && x.Operacao.Devolucoes.Any(y => y.Status == StatusOperacao.EFETUADA));
        }

        public async Task<MovimentacaoFinanceira> ObterInformacoesVincularBaixaNaFatura(Guid id)
        {
            return await DbSet.Where(x => x.Id == id)
                              .Select(x => new MovimentacaoFinanceira
                              {
                                  PlanoContaId = x.PlanoContaId,
                                  DataVencimento = x.DataVencimento,
                                  Operacao = new Operacao
                                  {
                                      LojaId = x.Operacao.LojaId,
                                      DataHoraUsuario = x.Operacao.DataHoraUsuario,
                                      DataEmissao = x.Operacao.DataEmissao
                                  }
                              })
                              .FirstOrDefaultAsync();
        }

        public async Task<List<MovimentacaoFinanceira>> ObterParaRecalcularGraficoContasPagarReceber(DateTime dataInicio, DateTime dataLimite)
        {
            return await DbSet
                            .AsNoTracking()
                            .Where(x => x.DataVencimento >= dataInicio &&
                                        x.DataVencimento < dataLimite &&
                                        (x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.CONTAS_PAGAR ||
                                         x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.PAGAMENTO_COMPRA ||
                                         x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.COMPRA_MERCADORIA ||
                                         x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.CONTAS_RECEBER ||
                                         x.Operacao.TipoOperacao.IdentificacaoTipoOperacao == IdentificacaoTipoOperacao.VENDA) &&
                                        x.Operacao.Status == StatusOperacao.EFETUADA)
                            .Select(x => new MovimentacaoFinanceira
                            {
                                DataVencimento = x.DataVencimento,
                                PlanoContaId = x.PlanoContaId,
                                Valor = x.Valor,
                                Operacao = new Operacao
                                {
                                    LojaId = x.Operacao.LojaId,
                                    DataEmissao = x.Operacao.DataEmissao,
                                    DataHoraUsuario = x.Operacao.DataHoraUsuario,
                                    TipoOperacao = new TipoOperacao { AcaoFinanceira = x.Operacao.TipoOperacao.AcaoFinanceira }
                                },
                                MovimentacoesFinanceirasBaixa = x.MovimentacoesFinanceirasBaixa.Select(y => new MovimentacaoFinanceiraBaixa
                                {
                                    Valor = y.Valor,
                                    Desconto = y.Desconto,
                                    Multa = y.Multa,
                                    Juros = y.Juros
                                }).ToList()
                            })
                            .ToListAsync();
        }

        public async Task<int> QntdParcelasPorAgrupamento(Guid identificadorAgrupamento)
        {
            return await DbSet.Where(x => x.IdentificadorAgrupamento == identificadorAgrupamento).CountAsync();
        }

        public async Task<List<MovimentacaoFinanceira>> ListarAgrupamento(Guid identificador)
        {
            return await DbSet.Where(m => m.IdentificadorAgrupamento == identificador).ToListAsync();
        }
    }
}
