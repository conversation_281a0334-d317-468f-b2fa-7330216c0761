﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net5.0</TargetFramework>
		<AzureFunctionsVersion>v4</AzureFunctionsVersion>
		<RootNamespace>zendar_import</RootNamespace>
		<RuntimeIdentifier>win-x64</RuntimeIdentifier>
		<PublishReadyToRun>true</PublishReadyToRun>
		<LangVersion>latest</LangVersion>
		<ImplicitUsings>disable</ImplicitUsings>
		<Nullable>disable</Nullable>
		<PlatformTarget>AnyCPU</PlatformTarget>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="MediatR" Version="10.0.1" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="10.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.2.7" />
		<PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="5.0.7" />
		<PackageReference Include="Azure.Core" Version="1.34.0" />
		<PackageReference Include="Microsoft.Extensions.Azure" Version="1.2.0" />
		<PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="5.0.1" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="5.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="5.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="5.0.0" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="5.0.0" />
		<PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.37" />
		<PackageReference Include="Microsoft.Azure.WebJobs.Extensions.ServiceBus" Version="5.7.0" />
		<PackageReference Include="Microsoft.Azure.WebJobs.Script.ExtensionsMetadataGenerator" Version="4.0.1" />
		<PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
		<PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.1.3" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Zendar.Business\Zendar.Business.csproj" />
		<ProjectReference Include="..\Zendar.Shared\Zendar.Shared.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Reference Include="Multiempresa.Data">
			<HintPath>DLL\Multiempresa.Data.dll</HintPath>
		</Reference>
		<Reference Include="Multiempresa.Shared">
			<HintPath>DLL\Multiempresa.Shared.dll</HintPath>
		</Reference>
		<Reference Include="ZendarPackage.NotaFiscal">
			<HintPath>DLL\ZendarPackage.NotaFiscal.dll</HintPath>
		</Reference>
	</ItemGroup>
	<ItemGroup>
		<None Update="host.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="local.settings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>
	<ItemGroup>
		<Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Properties\" />
	</ItemGroup>
</Project>