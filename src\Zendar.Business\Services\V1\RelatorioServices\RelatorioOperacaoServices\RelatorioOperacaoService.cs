﻿using Multiempresa.Shared.Helpers.Convertores;
using System;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Helpers.ImpressaoRelatorioPdf;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Relatorios;
using Zendar.Data.Enums;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels.Relatorio.Operacao;

namespace Zendar.Business.Services.RelatorioServices.RelatorioOperacaoServices
{
    public class RelatorioOperacaoService : BaseService, IRelatorioOperacaoService
    {
        private readonly IAspNetUserInfo _aspNetUserInfo;

        private readonly IPedidoOrcamentoVendaRepository _pedidoOrcamentoVendaRepository;
        private readonly IPedidoOrcamentoVendaItemRepository _pedidoOrcamentoVendaItemRepository;
        private readonly ILojaRepository _lojaRepository;
        private readonly IClienteFornecedorRepository _clienteFornecedorRepository;
        private readonly IOperacaoItemRemovidoRepository _operacaoItemRemovidoRepository;

        private readonly IProdutoCorTamanhoService _produtoCorTamanhoService;

        public RelatorioOperacaoService(INotificador notificador,
                                        IAspNetUserInfo aspNetUserInfo,
                                        IPedidoOrcamentoVendaRepository pedidoOrcamentoVendaRepository,
                                        ILojaRepository lojaRepository,
                                        IProdutoCorTamanhoService produtoCorTamanhoService,
                                        IClienteFornecedorRepository clienteFornecedorRepository,
                                        IPedidoOrcamentoVendaItemRepository pedidoOrcamentoVendaItemRepository,
                                        IOperacaoItemRemovidoRepository operacaoItemRemovidoRepository) : base(notificador)
        {
            _aspNetUserInfo = aspNetUserInfo;
            _pedidoOrcamentoVendaRepository = pedidoOrcamentoVendaRepository;
            _lojaRepository = lojaRepository;
            _produtoCorTamanhoService = produtoCorTamanhoService;
            _clienteFornecedorRepository = clienteFornecedorRepository;
            _pedidoOrcamentoVendaItemRepository = pedidoOrcamentoVendaItemRepository;
            _operacaoItemRemovidoRepository = operacaoItemRemovidoRepository;
        }

        public async Task<byte[]> GerarRelatorioComissao(FiltroRelatorioComissaoViewModel filtros)
        {
            switch (filtros.TipoRelatorioConsignacao)
            {
                case TipoRelatorioConsignacao.LISTAGEM_PRODUTOS:
                    return await ListagemItensConsignados(filtros);
                case TipoRelatorioConsignacao.AGRUPADO_POR_CLIENTES:
                    return await ListagemItensConsignadosAgrupadoPorCliente(filtros);
            }

            return null;
        }

        public async Task<byte[]> GerarRelatorioItensRemovidos(FiltroRelatorioRegistroItensRemovidos filtros)
        {
            return await ListagemItensRemovidos(filtros);
        }

        private async Task<byte[]> ListagemItensConsignados(FiltroRelatorioComissaoViewModel filtros)
        {
            var itens = await _pedidoOrcamentoVendaItemRepository.ObterListagemItensConsignadosParaRelatorio(filtros, _aspNetUserInfo.LojaId.Value);

            if (itens == null || itens.Count() == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, itens);

            var relatorioVm = new RelatorioListagemItensConsignadosViewModel(await ObterViewModelBase(filtros));

            relatorioVm.Itens = itens.OrderBy(i => i.Operacao.DataEmissao).Select(i => new ItemConsignadoRelatorioViewModel
            {
                Data = $"{i.Operacao.DataEmissao:dd/MM/yyyy}",
                Produto = filtros.ProdutoCorTamanhoId.HasValue ? filtros.Produto : _produtoCorTamanhoService.FormatarDescricaoCompletaProduto(i.ProdutoCorTamanhoId).Result,
                Devolvido = i.Status == Data.Enums.StatusOperacaoItem.DEVOLVIDO,
                Vendido = i.ItemVendaConsignacaoId.HasValue,
                Quantidade = i.Quantidade,
                ValorTotal = i.ValorItemComDesconto,
                NumeroOperacao = i.Operacao.NumeroOperacao
            }).ToList();

            return new ImpressaoRelatorioItensConsignados(relatorioVm).ToArray();
        }

        private async Task<byte[]> ListagemItensConsignadosAgrupadoPorCliente(FiltroRelatorioComissaoViewModel filtros)
        {
            var itens = await _pedidoOrcamentoVendaItemRepository.ObterItensConsignadosPorClienteParaRelatorio(filtros, _aspNetUserInfo.LojaId.Value);

            if (itens == null || itens.Count() == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, itens);

            var relatorioVm = new RelatorioItensConsignadosPorClienteViewModel(await ObterViewModelBase(filtros));

            relatorioVm.Clientes = itens.OrderBy(i => i.Operacao.DataEmissao).GroupBy(i => i.Operacao.ClienteFornecedor.Id).Select(c =>
            {
                var cliente = new ClienteRelatorioItensConsignadosPorClienteViewModel();

                var first = c.First().Operacao.ClienteFornecedor;

                cliente.Cliente = first.Nome + (string.IsNullOrEmpty(first.Apelido) ? string.Empty : $" | {first.Apelido}");

                var telefone = first.Telefone;
                if (string.IsNullOrEmpty(telefone) || telefone.Length == 0)
                    telefone = first.Celular;
                else if (!string.IsNullOrEmpty(first.Celular))
                    telefone += $" / {first.Celular}";

                cliente.Telefone = telefone;

                cliente.Itens = c.Select(i => new ItemConsignadoRelatorioViewModel
                {
                    Data = $"{i.Operacao.DataEmissao:dd/MM/yyyy}",
                    NumeroOperacao = i.Operacao.NumeroOperacao,
                    Produto = filtros.ProdutoCorTamanhoId.HasValue ? filtros.Produto : _produtoCorTamanhoService.FormatarDescricaoCompletaProduto(i.ProdutoCorTamanhoId).Result,
                    Devolvido = i.Status == Data.Enums.StatusOperacaoItem.DEVOLVIDO,
                    Vendido = i.ItemVendaConsignacaoId.HasValue,
                    Quantidade = i.Quantidade,
                    ValorTotal = i.ValorItemComDesconto
                }).ToList();

                return cliente;
            }).ToList();

            return new ImpressaoRelatorioItensConsignadosPorCliente(relatorioVm).ToArray();
        }

        private async Task<byte[]> ListagemItensRemovidos(FiltroRelatorioRegistroItensRemovidos filtros)
        {
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtros);

            var itensRemovidos = await _operacaoItemRemovidoRepository.ObterItensRemovidosParaRelatorio(filtros, _aspNetUserInfo.LojaId.Value);                   

            if (itensRemovidos == null || itensRemovidos.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == _aspNetUserInfo.LojaId.Value, x => new Loja { Fantasia = x.Fantasia });

            var relatorio = new RelatorioOperacaoItemRemovidoViewModel
            {
                TipoSistema = _aspNetUserInfo.Sistema,
                Fantasia = loja?.Fantasia,
                Emissao = DateTime.UtcNow.AddHours(_aspNetUserInfo.TimezoneOffset.Value),
                Filtro = filtros,

                Itens = itensRemovidos.Select(i => new ItemRemovidoViewModel
                {
                    DataHora = i.DataHora.Value,
                    LiberadoPor = i.UsuarioLiberacao.Nome,
                    Motivo = i.Motivo,
                    UsuarioNome = i.Usuario.Nome,
                    ProdutoNome = i.Produto.Nome,
                    VendedorNome = i.Vendedor.Nome,
                    Quantidade = i.Quantidade,
                    Valor = i.Valor,
                    Total = i.Valor * i.Quantidade,
                }).ToList()
            };
            return new ImpressaoRelatorioItensRemovidos(relatorio).ToArray();
        }

        private async Task<RelatorioConsignacaoViewModel> ObterViewModelBase(FiltroRelatorioComissaoViewModel filtros)
        {
            ConverterDate.AplicarUTC(_aspNetUserInfo.TimezoneOffset.Value, filtros);

            // Preencher filtro cliente
            if (filtros.ClienteFornecedorId.HasValue)
                filtros.Cliente = await _clienteFornecedorRepository.ObterNome(filtros.ClienteFornecedorId.Value);

            // Preencher filtro produto
            if (filtros.ProdutoCorTamanhoId.HasValue)
                filtros.Produto = await _produtoCorTamanhoService.FormatarDescricaoCompletaProduto(filtros.ProdutoCorTamanhoId);

            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == _aspNetUserInfo.LojaId.Value, x => new Loja { Fantasia = x.Fantasia });

            return new RelatorioConsignacaoViewModel
            {
                TipoSistema = _aspNetUserInfo.Sistema,
                Fantasia = loja?.Fantasia,
                Emissao = DateTime.UtcNow.AddHours(_aspNetUserInfo.TimezoneOffset.Value),
                Filtro = filtros,
            };
        }

        public void Dispose()
        {
            _pedidoOrcamentoVendaRepository?.Dispose();
            _lojaRepository?.Dispose();
            _clienteFornecedorRepository?.Dispose();
            _produtoCorTamanhoService?.Dispose();
        }
    }
}
