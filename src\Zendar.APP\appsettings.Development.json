{
  "ConnectionStrings": {
    //DEV
    //"DefaultConnection": "Data Source=tcp:sti3-dev.database.windows.net,1433;Initial Catalog=varejo;User Id=admin-dev;Password=***************;MultipleActiveResultSets=true;",
    //"MultiEmpresaConnection": "Data Source=tcp:sti3-dev.database.windows.net,1433;Initial Catalog=multiempresa;User Id=admin-dev;Password=***************;MultipleActiveResultSets=true;",

    //HOM
    //"DefaultConnection": "Data Source=tcp:zendar-homolog.database.windows.net,1433;Initial Catalog=qtal;User Id=zendar-homolog-admin;Password=***************;MultipleActiveResultSets=true;",
    //"MultiEmpresaConnection": "Data Source=tcp:multiempresa-homolog.database.windows.net,1433;Initial Catalog=multiempresa_oficial;User Id=multiempresa-homolog-admin;Password=***************;MultipleActiveResultSets=true;"

    //LOCALHOST
    "DefaultConnection": "Server=localhost;Database=dragornia;Trusted_Connection=True;persist security info=True;MultipleActiveResultSets=True;",
    "MultiEmpresaConnection": "Server=localhost;Database=stargateDev;Trusted_Connection=True;persist security info=True;MultipleActiveResultSets=True;"
  },
  "StorageSettings": {
    //DEV
    "App": "DefaultEndpointsProtocol=https;AccountName=zendarappdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarappdev.blob.core.windows.net/;QueueEndpoint=https://zendarappdev.queue.core.windows.net/;TableEndpoint=https://zendarappdev.table.core.windows.net/;FileEndpoint=https://zendarappdev.file.core.windows.net/;",
    "ArquivosFiscais": "DefaultEndpointsProtocol=https;AccountName=zendararquivosfiscaisdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararquivosfiscaisdev.blob.core.windows.net/;QueueEndpoint=https://zendararquivosfiscaisdev.queue.core.windows.net/;TableEndpoint=https://zendararquivosfiscaisdev.table.core.windows.net/;FileEndpoint=https://zendararquivosfiscaisdev.file.core.windows.net/;",
    "Certificados": "DefaultEndpointsProtocol=https;AccountName=zendarcertificadosdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarcertificadosdev.blob.core.windows.net/;QueueEndpoint=https://zendarcertificadosdev.queue.core.windows.net/;TableEndpoint=https://zendarcertificadosdev.table.core.windows.net/;FileEndpoint=https://zendarcertificadosdev.file.core.windows.net/;",
    "Imagens": "DefaultEndpointsProtocol=https;AccountName=zendarimagensdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimagensdev.blob.core.windows.net/;QueueEndpoint=https://zendarimagensdev.queue.core.windows.net/;TableEndpoint=https://zendarimagensdev.table.core.windows.net/;FileEndpoint=https://zendarimagensdev.file.core.windows.net/;",
    "Danfes": "DefaultEndpointsProtocol=https;AccountName=zendardanfesdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendardanfesdev.blob.core.windows.net/;QueueEndpoint=https://zendardanfesdev.queue.core.windows.net/;TableEndpoint=https://zendardanfesdev.table.core.windows.net/;FileEndpoint=https://zendardanfesdev.file.core.windows.net/;",
    "Importacao": "DefaultEndpointsProtocol=https;AccountName=zendarimportacaodev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimportacaodev.blob.core.windows.net/;QueueEndpoint=https://zendarimportacaodev.queue.core.windows.net/;TableEndpoint=https://zendarimportacaodev.table.core.windows.net/;FileEndpoint=https://zendarimportacaodev.file.core.windows.net/;",
    "Backup": "DefaultEndpointsProtocol=https;AccountName=zendarbackupdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarbackupdev.blob.core.windows.net/;QueueEndpoint=https://zendarbackupdev.queue.core.windows.net/;TableEndpoint=https://zendarbackupdev.table.core.windows.net/;FileEndpoint=https://zendarbackupdev.file.core.windows.net/;",
    "Relatorios": "DefaultEndpointsProtocol=https;AccountName=zendarrelatoriosdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarrelatoriosdev.blob.core.windows.net/;QueueEndpoint=https://zendarrelatoriosdev.queue.core.windows.net/;TableEndpoint=https://zendarrelatoriosdev.table.core.windows.net/;FileEndpoint=https://zendarrelatoriosdev.file.core.windows.net/;",
    "ArquivosTemporarios": "DefaultEndpointsProtocol=https;AccountName=zendararqtempdev;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararqtempdev.blob.core.windows.net/;QueueEndpoint=https://zendararqtempdev.queue.core.windows.net/;TableEndpoint=https://zendararqtempdev.table.core.windows.net/;FileEndpoint=https://zendararqtempdev.file.core.windows.net/;"

    //HOM
    //"App": "DefaultEndpointsProtocol=https;AccountName=zendarapphom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarapphom.blob.core.windows.net/;QueueEndpoint=https://zendarapphom.queue.core.windows.net/;TableEndpoint=https://zendarapphom.table.core.windows.net/;FileEndpoint=https://zendarapphom.file.core.windows.net/;",
    //"ArquivosFiscais": "DefaultEndpointsProtocol=https;AccountName=zendararquivosfiscaishom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararquivosfiscaishom.blob.core.windows.net/;QueueEndpoint=https://zendararquivosfiscaishom.queue.core.windows.net/;TableEndpoint=https://zendararquivosfiscaishom.table.core.windows.net/;FileEndpoint=https://zendararquivosfiscaishom.file.core.windows.net/;",
    //"Certificados": "DefaultEndpointsProtocol=https;AccountName=zendarcertificadoshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarcertificadoshom.blob.core.windows.net/;QueueEndpoint=https://zendarcertificadoshom.queue.core.windows.net/;TableEndpoint=https://zendarcertificadoshom.table.core.windows.net/;FileEndpoint=https://zendarcertificadoshom.file.core.windows.net/;",
    //"Imagens": "DefaultEndpointsProtocol=https;AccountName=zendarimagenshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimagenshom.blob.core.windows.net/;QueueEndpoint=https://zendarimagenshom.queue.core.windows.net/;TableEndpoint=https://zendarimagenshom.table.core.windows.net/;FileEndpoint=https://zendarimagenshom.file.core.windows.net/;",
    //"Danfes": "DefaultEndpointsProtocol=https;AccountName=zendardanfeshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendardanfeshom.blob.core.windows.net/;QueueEndpoint=https://zendardanfeshom.queue.core.windows.net/;TableEndpoint=https://zendardanfeshom.table.core.windows.net/;FileEndpoint=https://zendardanfeshom.file.core.windows.net/;",
    //"Importacao": "DefaultEndpointsProtocol=https;AccountName=zendarimportacaohom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarimportacaohom.blob.core.windows.net/;QueueEndpoint=https://zendarimportacaohom.queue.core.windows.net/;TableEndpoint=https://zendarimportacaohom.table.core.windows.net/;FileEndpoint=https://zendarimportacaohom.file.core.windows.net/;",
    //"Backup": "DefaultEndpointsProtocol=https;AccountName=zendarbackuphom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarbackuphom.blob.core.windows.net/;QueueEndpoint=https://zendarbackuphom.queue.core.windows.net/;TableEndpoint=https://zendarbackuphom.table.core.windows.net/;FileEndpoint=https://zendarbackuphom.file.core.windows.net/;",
    //"Relatorios": "DefaultEndpointsProtocol=https;AccountName=zendarrelatorioshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendarrelatorioshom.blob.core.windows.net/;QueueEndpoint=https://zendarrelatorioshom.queue.core.windows.net/;TableEndpoint=https://zendarrelatorioshom.table.core.windows.net/;FileEndpoint=https://zendarrelatorioshom.file.core.windows.net/;"
    //"ArquivosTemporarios": "DefaultEndpointsProtocol=https;AccountName=zendararqtemporarioshom;AccountKey=****************************************************************************************;BlobEndpoint=https://zendararqtemporarioshom.blob.core.windows.net/;QueueEndpoint=https://zendararqtemporarioshom.queue.core.windows.net/;TableEndpoint=https://zendararqtemporarioshom.table.core.windows.net/;FileEndpoint=https://zendararqtemporarioshom.file.core.windows.net/;"
  },
  "JwtSettings": {
    "Chave": "5P2Py9hgQPPVwCmfDmgkrSHmUkTT8LgKeFCnmLSSBgALbURm7JYBbgg53qpKXLKe678nUxwJLtBHysUVdASxA5PC3cq2f4r58yDuNRH4K8tZUfv9jBCWuYA87ZqY5MLNdrAe2e7kXHGkGBkhe6MLh5EZjvKMJd6SVHxMrNs5v6hhP8mXYbT4a8fuS2SLvVkxkpwrHGgTt3Uen23PEmUaPZb57ca7e7YZAhPHMLDynFKvPgjN5mraNkRD97hKdRFY",
    "ExpiracaoMinutos": 20,
    "Emissor": "Zendar-Dev",
    "ValidoEm": "https://zendar-dev-api.azurewebsites.net"
  },
  "TemporaryAccess": {
    "Chave": "H@McQfTjWnZr4u7x!A%D*F-JaNdRgUkXp2s5v8y/B?E(H+KbPeShVmYq3t6w9z$C",
    "ExpiracaoToken": 5
  },
  "LoginAplicacaoSettings": {
    "Login": "fomer-delivery",
    "Hash": "AQAAAAEAACcQAAAAEE/k8Lvchwf7XrEg0g6yN7hXiUZhzQvOA74my+vVyUztUP3reK30CHwJ5NHbuqaRow=="
  },
  "Logging": {
    "LogLevel": {
      "Default": "Error",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Error",
      "Microsoft.EntityFrameworkCore.Database.Command": "Debug" // Debug ou None - Log query do banco
      //"Hangfire": "Trace"
    }
  },
  "EmailSettings": {
    "EmailsLogErro": ""
  },
  "AllowedHosts": "*",
  "ServiceBusSettings": {
    "DefaultConnection": "Endpoint=sb://sb-zendar-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=5XClu79yk4mibM88xXcFNbXw5kTh298mw+ASbIyTRsg="
  },
  "MOVIDESK_TOKEN_API": "309afd53-b7d3-437c-ba7c-cdb169dda796",
  "SmartPOSSettings": {
    "EmailsParceiroStone": "<EMAIL>"
  },
  "ApiKey": "5e68c70f-b296-47f5-b7ca-bfd9cea9a159",
  "ZendarSyncApi": {
    "TrayUrl": "https://zendar-sync-tray-dev-api.azurewebsites.net/api",
    "PdvAutonomoUrl": "https://zendar-sync-pdv-dev-api.azurewebsites.net/api",
    "FrenteCaixaUrl": "https://zendar-sync-frente-caixa-dev-api.azurewebsites.net/api"
  },
  "ApplicationInsights": {
    "InstrumentationKey": ""
  },
  "ZoopSettingsAPI": {
    "MarketplaceId": "1548082506bc468f8e1eef6e1cfdd6ef",
    "SplitRecipientId": "fed3a41252bb408c954ebb29e7ed7cca",
    "Authorization": "Basic enBrX3Rlc3RfYTZ3ZmJSdHoyNFlMRU9QWTdEWWc5b2U5Og==",
    "UrlApi": "https://api.zoop.ws/v1/marketplaces"
  },
  "RedisSettings": {
    "Endpoint": "redis-14634.c266.us-east-1-3.ec2.redns.redis-cloud.com",
    "Port": "14634",
    "Password": "3gvprhtyRKHjjMGc5wY05cJCPSSz1FOH",
    "Configuration": "redis-14634.c266.us-east-1-3.ec2.redns.redis-cloud.com:14634,password=3gvprhtyRKHjjMGc5wY05cJCPSSz1FOH,ssl=True,abortConnect=False", //public endpoint
    "InstanceName": "Zendar-Dev"
  }
}
