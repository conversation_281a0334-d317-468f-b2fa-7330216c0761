﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zendar.Business.ViewModels.V1.ImportacaoCadastroIntegracaoViewModels;

namespace Zendar.Business.Services.V1.ImportacaoCadastroIntegracaoService
{
	public interface IImportacaoCadastroIntegracaoService : IDisposable
	{
		Task EnviarProduto(
			Guid produtoId,
			Guid categoriaId);

		Task AlterarProduto(
			Guid produtoId,
			Guid novaCategoriaId,
			Guid antigaCategoriaId);

		Task<bool> ValidarSeCategoriaExiste(
			Guid categoriaId);

		Task EnviarCategoria(
			Guid categoriaId);

		Task EnviarCategoriasComProdutos(
			IEnumerable<CategoriaImportacaoCadastroViewModel> categoriasViewModel,
			Guid lojaId,
			Guid integracaoId);

		Task LimparRegistros(Guid integracaoId);
	}
}
