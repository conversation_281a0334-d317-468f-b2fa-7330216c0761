﻿using AutoMapper;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Multiempresa.Data.Repositories.NcmRepositories;
using Multiempresa.Shared.Constants;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers.Convertores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Business.Application.Events.ProdutoEvents;
using Zendar.Business.Helpers;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Interfaces.Services.OperacaoServices;
using Zendar.Business.Services.IntegracaoServices.CaixaMovel.IntegracaoCaixaMovelImportacaoCadastro;
using Zendar.Business.Services.IntegracaoServices.CaixaMovel.IntegracaoCaixaMovelProdutoService;
using Zendar.Business.Services.IntegracaoServices.STi3DashboardServices.GraficosProdutoSTi3DashboardService;
using Zendar.Business.Services.OperacaoItemServices.OperacaoItemObterServices;
using Zendar.Business.Services.PesquisaProdutoServices;
using Zendar.Business.Services.PesquisaProdutoServices.Popular;
using Zendar.Business.Services.V1.ImportacaoCadastroIntegracaoService;
using Zendar.Business.Services.V1.IntegracaoServices.SincronizacaoServices.Trigger;
using Zendar.Business.Services.V2.PromocaoV2Services.MelhorPromocaoValidaV2Service;
using Zendar.Business.Validations.Models;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Operacao;
using Zendar.Business.ViewModels.V1;
using Zendar.Business.ViewModels.V1.ProdutoAlteracaoEmMassa;
using Zendar.Data.Enums;
using Zendar.Data.Enums.Integracao;
using Zendar.Data.Enums.Stargate;
using Zendar.Data.Extensions;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.CampanhaPromocional;
using Zendar.Data.Models.GerenciadorDeImpressao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services
{
    public class ProdutoService : BaseService, IProdutoService
    {
        private readonly IProdutoRepository _produtoRepository;
        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;
        private readonly IProdutoCorTamanhoEstoqueRepository _produtoCorTamanhoEstoqueRepository;
        private readonly IProdutoCorRepository _produtoCorRepository;
        private readonly IProdutoCorTamanhoKitRepository _produtoCorTamanhoKitRepository;
        private readonly ICampoPersonalizadoRepository _campoPersonalizadoRepository;
        private readonly ITagProdutoRepository _tagProdutoRepository;
        private readonly IStorageService _storageService;
        private readonly ICorRepository _corRepository;
        private readonly ITamanhoRepository _tamanhoRepository;
        private readonly IMapper _mapper;
        private readonly IDatabaseTransaction _databaseTransaction;
        private readonly IZendarTriggerService _zendarTriggerService;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly INcmRepository _ncmRepository;
        private readonly IOperacaoService _operacaoService;
        private readonly ILocalEstoqueRepository _localEstoqueRepository;
        private readonly IOperacaoItemService _operacaoItemService;
        private readonly ICategoriaProdutoService _categoriaProdutoService;
        private readonly ILojaRepository _lojaRepository;
        private readonly IProdutoCorTamanhoService _produtoCorTamanhoService;
        private readonly IOperacaoItemObterService _operacaoItemObterService;
        private readonly IIntegracaoCaixaMovelImportacaoCadastroService _integracaoCaixaMovelImportacaoCadastroService;
        private readonly IGraficosProdutoSTi3DashboardService _graficosProdutoSTi3DashboardService;
        private readonly IIntegracaoCaixaMovelProdutoService _integracaoCaixaMovelProdutoService;
        private readonly ILojaService _lojaService;
        private readonly IPopularPesquisaProdutoService _popularPesquisaProdutoService;
        private readonly IPesquisaProdutoService _pesquisaProdutoService;
        private readonly IProdutoGerenciadorImpressaoRepository _produtoGerenciadorImpressaoRepository;
        private readonly IMelhorPromocaoValidaService _melhorPromocaoValidaService;
        private readonly IImportacaoCadastroIntegracaoService _importacaoCadastroIntegracaoService;

		public ProdutoService(INotificador notificador,
							  IProdutoRepository produtoRepository,
							  IProdutoCorTamanhoRepository produtoCorTamanhoRepository,
							  IProdutoCorRepository produtoCorRepository,
							  IProdutoCorTamanhoKitRepository produtoCorTamanhoKitRepository,
							  ICampoPersonalizadoRepository campoPersonalizadoRepository,
							  ITagProdutoRepository tagProdutoRepository,
							  IStorageService storageService,
							  ICorRepository corRepository,
							  ITamanhoRepository tamanhoRepository,
							  IMapper mapper,
							  IDatabaseTransaction databaseTransaction,
							  IZendarTriggerService zendarTriggerService,
							  ILogAuditoriaService logAuditoriaService,
							  IAspNetUserInfo aspNetUserInfo,
							  IProdutoCorTamanhoEstoqueRepository produtoCorTamanhoEstoqueRepository,
							  INcmRepository ncmRepository,
							  IOperacaoService operacaoService,
							  ILocalEstoqueRepository localEstoqueRepository,
							  IOperacaoItemService operacaoItemService,
							  ICategoriaProdutoService categoriaProdutoService,
							  ILojaRepository lojaRepository,
							  IProdutoCorTamanhoService produtoCorTamanhoService,
							  IOperacaoItemObterService operacaoItemObterService,
							  IGraficosProdutoSTi3DashboardService graficosProdutoSTi3DashboardService,
							  IIntegracaoCaixaMovelImportacaoCadastroService integracaoCaixaMovelImportacaoCadastroService,
							  IIntegracaoCaixaMovelProdutoService integracaoCaixaMovelProdutoService,
							  ILojaService lojaService,
							  IPopularPesquisaProdutoService popularPesquisaProdutoService,
							  IPesquisaProdutoService pesquisaProdutoService,
							  IProdutoGerenciadorImpressaoRepository produtoGerenciadorImpressaoRepository,
							  IMelhorPromocaoValidaService melhorPromocaoValidaService,
							  IImportacaoCadastroIntegracaoService importacaoCadastroIntegracaoService) : base(notificador)
		{
			_produtoRepository = produtoRepository;
			_produtoCorTamanhoRepository = produtoCorTamanhoRepository;
			_produtoCorRepository = produtoCorRepository;
			_produtoCorTamanhoKitRepository = produtoCorTamanhoKitRepository;
			_campoPersonalizadoRepository = campoPersonalizadoRepository;
			_tagProdutoRepository = tagProdutoRepository;
			_storageService = storageService;
			_corRepository = corRepository;
			_tamanhoRepository = tamanhoRepository;
			_mapper = mapper;
			_databaseTransaction = databaseTransaction;
			_zendarTriggerService = zendarTriggerService;
			_logAuditoriaService = logAuditoriaService;
			_aspNetUserInfo = aspNetUserInfo;
			_ncmRepository = ncmRepository;
			_produtoCorTamanhoEstoqueRepository = produtoCorTamanhoEstoqueRepository;
			_operacaoService = operacaoService;
			_localEstoqueRepository = localEstoqueRepository;
			_operacaoItemService = operacaoItemService;
			_categoriaProdutoService = categoriaProdutoService;
			_lojaRepository = lojaRepository;
			_produtoCorTamanhoService = produtoCorTamanhoService;
			_operacaoItemObterService = operacaoItemObterService;
			_graficosProdutoSTi3DashboardService = graficosProdutoSTi3DashboardService;
			_integracaoCaixaMovelImportacaoCadastroService = integracaoCaixaMovelImportacaoCadastroService;
			_integracaoCaixaMovelProdutoService = integracaoCaixaMovelProdutoService;
			_lojaService = lojaService;
			_popularPesquisaProdutoService = popularPesquisaProdutoService;
			_pesquisaProdutoService = pesquisaProdutoService;
			_produtoGerenciadorImpressaoRepository = produtoGerenciadorImpressaoRepository;
			_melhorPromocaoValidaService = melhorPromocaoValidaService;
			_importacaoCadastroIntegracaoService = importacaoCadastroIntegracaoService;
		}

		private async Task<IdNomeViewModel> Cadastrar(ProdutoViewModel produtoViewModel)
        {
            if (!ValidarPalavrasReservadas(produtoViewModel))
                return null;

            Produto produto = _mapper.Map<Produto>(produtoViewModel);
            produto.Id = Guid.Empty;

            if (produtoViewModel.Tags != null)
            {
                produto.TagProdutos = produtoViewModel.Tags.Select(tagId => new TagProduto() { TagId = tagId }).ToList();
            }

            if (produtoViewModel.CamposPersonalizados != null)
            {
                produto.CampoPersonalizadoProduto = produtoViewModel.CamposPersonalizados
                    .Select(campoPersonalizado => new CampoPersonalizadoProduto()
                    {
                        CampoPersonalizadoId = campoPersonalizado.CampoPersonalizadoId,
                        Valor = campoPersonalizado.Valor
                    })
                    .ToList();
            }

            if (!ExecutarValidacao(new ProdutoValidation(), produto)) return null;

            var quantidadeLojas = await _lojaRepository.ObterQuantidadeLojas();

            if (produto.ProdutoPrecoLojas.GroupBy(pcl => pcl.LojaId).Count() < quantidadeLojas)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_InformarPrecosLojas);
                return null;
            }

            foreach (var produtoPreco in produto.ProdutoPrecoLojas)
            {
                if (!ExecutarValidacao(new ProdutoPrecoLojaValidation(), produtoPreco)) return null;
            }

            if (await _produtoRepository.Any(x => x.Nome == produto.Nome))
            {
                NotificarAviso(ResourceMensagem.ProdutoService_JaCadastradoNomeInformado);
                return null;
            }

            if (await _produtoRepository.Any(x => !string.IsNullOrEmpty(produto.Referencia) && x.Referencia == produto.Referencia))
            {
                NotificarAviso(ResourceMensagem.ProdutoService_JaCadastradoReferenciaInformada);
                return null;
            }

            if (!string.IsNullOrEmpty(produto.Foto))
            {
                var caminhoImagem = string.Format(CaminhoArquivosStorage.CaminhoImagemProduto, Guid.NewGuid());

                //Verifica se a foto é uma URL
                if (Uri.TryCreate(produto.Foto, UriKind.Absolute, out Uri uri))
                {
                    //Pega o base64 da URL
                    var image = await ConverterArquivo.ConverterUrlImageBase64(produto.Foto);

                    if (!image.Item1)
                    {
                        NotificarAviso(ResourceMensagem.ProdutoService_FormatoImagemNaoPermitido);
                        return null;
                    }

                    produto.Foto = image.Item2;
                }

                await _storageService.Upload(StorageContaArmazenamento.Imagens, TipoArquivo.OUTROS, caminhoImagem, produto.Foto);
                produto.Foto = caminhoImagem;
            }

            var produtoCorTamanhoKitItens = new List<ProdutoCorTamanhoKit>();
            if (produto.TipoProduto == TipoProduto.PRODUTO_KIT)
            {
                var produtoCor = produto.ProdutoCores.FirstOrDefault();

                if (produtoCor != null)
                {
                    var produtoCorTamanho = produtoCor.ProdutoCorTamanhos.FirstOrDefault();

                    if (produtoCorTamanho != null && produtoCorTamanho.ProdutoCorTamanhoItens != null)
                    {
                        produtoCorTamanhoKitItens = produtoCorTamanho.ProdutoCorTamanhoItens.ToList();

                        produtoCorTamanho.ProdutoCorTamanhoItens = new List<ProdutoCorTamanhoKit>();
                    }
                }
            }

            _databaseTransaction.BeginTransaction();

            Cor corPadraoSistema = await _corRepository.FirstOrDefaultAsNoTracking(cor => cor.PadraoSistema, cor => new Cor() { Id = cor.Id });
            Tamanho tamanhoPadraoSistema = await _tamanhoRepository.FirstOrDefaultAsNoTracking(tamanho => tamanho.PadraoSistema, tamanho => new Tamanho() { Id = tamanho.Id });

            if (PossuiGtinEanRepetido(produtoViewModel))
                return null;

            foreach (var produtoCor in produto.ProdutoCores)
            {
                if (produtoCor.CorId == Guid.Empty)
                {
                    produtoCor.CorId = corPadraoSistema.Id;
                }

                foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                {
                    //Validacao para nao inserir produto com codigo externo ou gtin clonado
                    if (await VerificarCodExternoGTINEAN(Guid.Empty, produtoCorTamanho.CodigoExterno, produtoCorTamanho.CodigoGTINEAN))
                        return null;

                    if (produtoCorTamanho.TamanhoId == Guid.Empty)
                        produtoCorTamanho.TamanhoId = tamanhoPadraoSistema.Id;

                    if (produto.TipoProduto != TipoProduto.PRODUTO_VARIACAO)
                    {
                        produtoCorTamanho.Altura = produtoViewModel.OutrasInformacoesAltura;
                        produtoCorTamanho.Largura = produtoViewModel.OutrasInformacoesLargura;
                        produtoCorTamanho.Profundidade = produtoViewModel.OutrasInformacoesProfundidade;
                        produtoCorTamanho.PesoLiquido = produtoViewModel.OutrasInformacoesPesoLiquido;
                        produtoCorTamanho.PesoBruto = produtoViewModel.OutrasInformacoesPesoBruto;
                    }

                    produtoCorTamanho.CodigoBarrasInterno = "5000000000005";

                    produtoCorTamanho.ProdutoCorTamanhoEstoques = new List<ProdutoCorTamanhoEstoque>();
                }
            }

            await _produtoRepository.Insert(produto);

            if (produto.TipoProduto == TipoProduto.PRODUTO_KIT)
            {
                var produtoCor = produto.ProdutoCores.FirstOrDefault();

                if (produtoCor != null)
                {
                    var produtoCorTamanho = produtoCor.ProdutoCorTamanhos.FirstOrDefault();

                    if (produtoCorTamanho != null)
                    {
                        var lojas = await _lojaRepository.ListarTodasLojas();

                        produtoCorTamanhoKitItens = produtoCorTamanhoKitItens
                            .Select(p => new ProdutoCorTamanhoKit()
                            {
                                Quantidade = p.Quantidade,
                                ProdutoCorTamanhoKitPrecoLojas = lojas.Select(l => new ProdutoCorTamanhoKitPrecoLoja
                                {
                                    LojaId = l.Id,
                                    Valor = produtoViewModel.ProdutoCores.First().ProdutoCorTamanhos.First().ProdutoCorTamanhoKitItens.First(x => x.ProdutoCorTamanhoItemId.Equals(p.ProdutoCorTamanhoItemId)).Valor
                                }).ToList(),
                                ProdutoCorTamanhoItemId = p.ProdutoCorTamanhoItemId,
                                ProdutoCorTamanhoPrincipalId = produtoCorTamanho.Id
                            })
                            .ToList();

                        foreach (var produtoCorTamanhoKitItem in produtoCorTamanhoKitItens)
                        {
                            await _produtoCorTamanhoKitRepository.Insert(produtoCorTamanhoKitItem);
                        }
                    }
                }
            }

            if (produtoViewModel.TipoProduto != TipoProduto.PRODUTO_KIT)
            {
                var produtoOperacaoViewModel = new ProdutoViewModel
                {
                    ProdutoCores = produtoViewModel.ProdutoCores.Where(x => x.ProdutoCorTamanhos.Any(x => x.ProdutoCorTamanhoEstoques.Any(x => x.EstoqueAtual > 0)))
                                                   .Select(x => new ProdutoCorViewModel
                                                   {
                                                       ProdutoCorTamanhos = x.ProdutoCorTamanhos.Select(y => new ProdutoCorTamanhoViewModel
                                                       {
                                                           Id = produto.ProdutoCores.Where(pc => pc.CorId.Equals(x.CorId.HasValue ? x.CorId.Value : corPadraoSistema.Id)
                                                                                              && pc.ProdutoCorTamanhos.Any(pct => pct.TamanhoId.Equals(y.TamanhoId.HasValue ? y.TamanhoId.Value : tamanhoPadraoSistema.Id)))
                                                                                    .FirstOrDefault().ProdutoCorTamanhos.FirstOrDefault(v => y.TamanhoId.Equals(v.TamanhoId) || y.TamanhoId == null).Id,
                                                           ProdutoCorTamanhoEstoques = y.ProdutoCorTamanhoEstoques
                                                       }).ToArray()
                                                   }).ToArray()
                };

                if (produtoOperacaoViewModel.ProdutoCores.Count() > 0)
                {
                    await GerarOperacaoDeEntradaEstoque(produtoOperacaoViewModel);
                }
            }

            await _logAuditoriaService
               .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.PRODUTO, LogAuditoriaOperacao.CADASTRAR, $"Nome: {produto.Nome}"));

            _databaseTransaction.Commit();

            #region Trigger

            await _zendarTriggerService.ExecuteGuid(produto.Id,
                                                TabelaTrigger.PRODUTO,
                                                OperacaoTrigger.CADASTRAR);

            #endregion

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produto.Id);

            await _integracaoCaixaMovelProdutoService.EnviarProduto(produto.Id, produto.CategoriaProdutoId);

            return new IdNomeViewModel { Id = produto.Id, Nome = produto.Nome };
        }

        public async Task<IdNomeViewModel> CadastrarPelaTela(ProdutoViewModel produtoViewModel)
        {
            return await Cadastrar(produtoViewModel);
        }

        public async Task<ProdutoCadastroModalRetornoViewModel> CadastrarPeloModal(ProdutoViewModel produtoViewModel)
        {
            var produto = await Cadastrar(produtoViewModel);

            if (produto == null)
            {
                return null;
            }

            var produtoCores = await _produtoCorRepository.ObterProdutorCorEntradaMercadoria(produto.Id.Value);

            var produtoCadastroModalRetorno = new ProdutoCadastroModalRetornoViewModel
            {
                Id = produto.Id.Value,
                Nome = produto.Nome,
                ProdutoCores = produtoCores.Select(x => new ProdutoCorTamanhoListaSelectEntradaMercadoriaViewModel
                {
                    CorDescricao = x.Cor.Descricao,
                    CorId = x.Cor.Id,
                    CorPadraoSistema = x.Cor.PadraoSistema,
                    Tamanhos = x.ProdutoCorTamanhos.Select(y => new ListaIdNomePadraoSistemaViewModel
                    {
                        Id = y.Tamanho.Id,
                        Nome = y.Tamanho.Descricao,
                        PadraoSistema = y.Tamanho.PadraoSistema
                    }).ToList()
                }).ToList()
            };

            return produtoCadastroModalRetorno;
        }

        public async Task Alterar(ProdutoViewModel produtoViewModel)
        {
            if (!ValidarPalavrasReservadas(produtoViewModel))
                return;

            if (!ExecutarValidacao(new ProdutoValidation(), _mapper.Map<Produto>(produtoViewModel))) return;

            if (await _produtoRepository.Any(x => !string.IsNullOrEmpty(produtoViewModel.Referencia) && x.Referencia == produtoViewModel.Referencia && !x.Id.Equals(produtoViewModel.Id)))
            {
                NotificarAviso(ResourceMensagem.ProdutoService_JaCadastradoReferenciaInformada);
                return;
            }

            var quantidadeLojas = await _lojaRepository.ObterQuantidadeLojas();

            if (produtoViewModel.ProdutoPrecoLojas.GroupBy(pcl => pcl.LojaId).Count() < quantidadeLojas)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_InformarPrecosLojas);
                return;
            }

            foreach (var produtoPreco in produtoViewModel.ProdutoPrecoLojas)
            {
                if (!ExecutarValidacao(new ProdutoPrecoLojaValidation(), _mapper.Map<ProdutoPrecoLoja>(produtoPreco))) return;
            }

            var produtoAtualizado = await _produtoRepository.ObterParaAlteracao(produtoViewModel.Id);

            if (produtoAtualizado == null)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_NaoEncontrado);
                return;
            }

            if (await _produtoRepository.Any(x => x.Nome == produtoViewModel.Nome && x.Id != produtoViewModel.Id))
            {
                NotificarAviso(ResourceMensagem.ProdutoService_JaCadastradoNomeInformado);
                return;
            }

            var logAuditoria = new LogAuditoriaInserirViewModel(LogAuditoriaTela.PRODUTO, LogAuditoriaOperacao.ALTERAR,
                Formatter.LogAuditoriaObservacaoPadrao(produtoAtualizado.Nome, produtoViewModel.Nome, nameof(produtoAtualizado.Nome)));

            var produtoPrecoCategoriaAlteradoEvent = new ProdutoPrecoCategoriaAlteradoEvent(produtoAtualizado, produtoViewModel);

            _databaseTransaction.BeginTransaction();

            if (produtoAtualizado.Foto != _storageService.ObterCaminhoArquivoBancoDados(StorageContaArmazenamento.Imagens, produtoViewModel.Foto))
            {
                if (!string.IsNullOrEmpty(produtoAtualizado.Foto))
                    await _storageService.Excluir(StorageContaArmazenamento.Imagens, produtoAtualizado.Foto);

                if (!string.IsNullOrEmpty(produtoViewModel.Foto))
                {
                    var caminhoImagem = string.Format(CaminhoArquivosStorage.CaminhoImagemProduto, Guid.NewGuid());

                    //Verifica se a foto é uma URL
                    if (Uri.TryCreate(produtoViewModel.Foto, UriKind.Absolute, out Uri uri))
                    {
                        //Pega o base64 da URL
                        var image = await ConverterArquivo.ConverterUrlImageBase64(produtoViewModel.Foto);

                        if (!image.Item1)
                        {
                            NotificarAviso(ResourceMensagem.ProdutoService_FormatoImagemNaoPermitido);
                            return;
                        }

                        produtoViewModel.Foto = image.Item2;
                    }

                    await _storageService.Upload(StorageContaArmazenamento.Imagens, TipoArquivo.OUTROS, caminhoImagem, produtoViewModel.Foto);

                    produtoAtualizado.Foto = caminhoImagem;
                }
            }

            Cor corPadraoSistema = await _corRepository.FirstOrDefaultAsNoTracking(cor => cor.PadraoSistema, cor => new Cor() { Id = cor.Id });
            Tamanho tamanhoPadraoSistema = await _tamanhoRepository.FirstOrDefaultAsNoTracking(tamanho => tamanho.PadraoSistema, tamanho => new Tamanho() { Id = tamanho.Id });

            if (PossuiGtinEanRepetido(produtoViewModel))
                return;

            #region [ Variações ]
            var produtoOperacao = new Produto();
            produtoOperacao.ProdutoCores = new List<ProdutoCor>();

            if (produtoViewModel.AtualizarVariacoes)
            {
                if (produtoAtualizado.TipoProduto == TipoProduto.PRODUTO_VARIACAO)
                {
                    foreach (var produtoCor in produtoViewModel.ProdutoCores)
                    {
                        if (produtoCor.CorId == Guid.Empty || produtoCor.CorId == null)
                        {
                            produtoCor.CorId = corPadraoSistema.Id;
                        }
                    }

                    var produtoCorAdicionar = produtoViewModel.ProdutoCores
                        .Where(x => x.CorId != null && x.CorId != Guid.Empty && x.CorId != corPadraoSistema.Id && produtoAtualizado.ProdutoCores
                           .All(y => y.CorId != x.CorId))
                        .ToList();

                    foreach (var produtoCorViewModel in produtoCorAdicionar)
                    {
                        var produtoCor = _mapper.Map<ProdutoCor>(produtoCorViewModel);

                        if (produtoCor.CorId == Guid.Empty)
                        {
                            produtoCor.CorId = corPadraoSistema.Id;
                        }

                        produtoCor.ProdutoCorTamanhos = new List<ProdutoCorTamanho>();

                        foreach (var produtoCorTamanhoViewModel in produtoCorViewModel.ProdutoCorTamanhos)
                        {
                            var produtoCorTamanho = _mapper.Map<ProdutoCorTamanho>(produtoCorTamanhoViewModel);

                            if (produtoCorTamanho.TamanhoId == Guid.Empty)
                            {
                                produtoCorTamanho.TamanhoId = tamanhoPadraoSistema.Id;
                                produtoCorTamanhoViewModel.TamanhoId = tamanhoPadraoSistema.Id;
                            }

                            produtoCorTamanho.CodigoBarrasInterno = "5000000000005";

                            produtoCorTamanho.ProdutoCorTamanhoEstoques = new List<ProdutoCorTamanhoEstoque>();

                            produtoCor.ProdutoCorTamanhos.Add(produtoCorTamanho);
                        }

                        produtoAtualizado.ProdutoCores.Add(produtoCor);
                        produtoOperacao.ProdutoCores.Add(new ProdutoCor
                        {
                            CorId = produtoCor.CorId,
                            ProdutoCorTamanhos = _mapper.Map<List<ProdutoCorTamanho>>(produtoCorViewModel.ProdutoCorTamanhos)
                        });
                    }

                    if (produtoAtualizado.ProdutoCores.Count > 1)
                    {
                        var produtoCorUnica = produtoAtualizado.ProdutoCores.FirstOrDefault(p => p.CorId.Equals(corPadraoSistema.Id));

                        // Valida se o produto não foi vendido antes de excluir
                        if (produtoCorUnica != null && !await _produtoCorTamanhoService.ProdutoFoiVendido(produtoCorUnica.ProdutoCorTamanhos.ToList()))
                        {
                            await _produtoCorRepository.Delete(produtoCorUnica.Id);
                            produtoAtualizado.ProdutoCores.Remove(produtoCorUnica);
                        }
                    }

                    foreach (var produtoCor in produtoAtualizado.ProdutoCores)
                    {
                        var produtoCorViewModel = produtoViewModel.ProdutoCores.FirstOrDefault(x => x.CorId == produtoCor.CorId);
                        if (produtoCorViewModel != null)
                        {
                            foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                            {
                                var produtoCorTamanhoViewModel = produtoCorViewModel.ProdutoCorTamanhos
                                    .FirstOrDefault(x => x.TamanhoId == produtoCorTamanho.TamanhoId ||
                                        ((x.TamanhoId == null || x.TamanhoId == Guid.Empty) && produtoCorTamanho.TamanhoId.Equals(tamanhoPadraoSistema.Id)));
                                if (produtoCorTamanhoViewModel != null)
                                {
                                    //Validacao para nao inserir produto com codigo externo ou gtin clonado
                                    if (await VerificarCodExternoGTINEAN(produtoCorTamanho.Id, produtoCorTamanhoViewModel.CodigoExterno, produtoCorTamanhoViewModel.CodigoGTINEAN))
                                        return;

                                    produtoCorTamanho.Altura = produtoCorTamanhoViewModel.Altura;
                                    produtoCorTamanho.Largura = produtoCorTamanhoViewModel.Largura;
                                    produtoCorTamanho.Profundidade = produtoCorTamanhoViewModel.Profundidade;
                                    produtoCorTamanho.PesoLiquido = produtoCorTamanhoViewModel.PesoLiquido;
                                    produtoCorTamanho.PesoBruto = produtoCorTamanhoViewModel.PesoBruto;

                                    produtoCorTamanho.EstoqueMinimo = produtoCorTamanhoViewModel.EstoqueMinimo;

                                    produtoCorTamanho.CodigoGTINEAN = produtoCorTamanhoViewModel.CodigoGTINEAN;
                                    produtoCorTamanho.CodigoExterno = produtoCorTamanhoViewModel.CodigoExterno;

                                    produtoCorTamanho.DataHoraUltimaAlteracao = DateTime.UtcNow;
                                }
                            }

                            var produtoCorTamanhosAdicionar = produtoCorViewModel.ProdutoCorTamanhos
                                      .Where(x => x.TamanhoId != null && x.TamanhoId != Guid.Empty && x.TamanhoId != tamanhoPadraoSistema.Id && produtoCor.ProdutoCorTamanhos.All(y => y.TamanhoId != x.TamanhoId))
                                      .ToList();

                            foreach (var produtoCorTamanhoViewModel in produtoCorTamanhosAdicionar)
                            {
                                var produtoCorTamanho = _mapper.Map<ProdutoCorTamanho>(produtoCorTamanhoViewModel);

                                if (produtoCorTamanho.TamanhoId == Guid.Empty)
                                {
                                    produtoCorTamanho.TamanhoId = tamanhoPadraoSistema.Id;
                                    produtoCorTamanhoViewModel.TamanhoId = tamanhoPadraoSistema.Id;
                                }

                                produtoCorTamanho.CodigoBarrasInterno = "5000000000005";

                                produtoCorTamanho.ProdutoCorTamanhoEstoques = new List<ProdutoCorTamanhoEstoque>();

                                produtoCor.ProdutoCorTamanhos.Add(produtoCorTamanho);
                            }

                            if (produtoCor.ProdutoCorTamanhos.Count > 1)
                            {
                                var produtoCorTamanhoUnico = produtoCor.ProdutoCorTamanhos.FirstOrDefault(p => p.TamanhoId.Equals(tamanhoPadraoSistema.Id));

                                // Valida se o produto não foi vendido antes de excluir
                                if (produtoCorTamanhoUnico != null && !await _produtoCorTamanhoService.ProdutoFoiVendido(new() { produtoCorTamanhoUnico }))
                                {
                                    await _produtoCorTamanhoRepository.Delete(produtoCorTamanhoUnico.Id);
                                    produtoCor.ProdutoCorTamanhos.Remove(produtoCorTamanhoUnico);
                                }
                            }

                            if (produtoCorTamanhosAdicionar != null && produtoCorTamanhosAdicionar.Count() > 0)
                                produtoOperacao.ProdutoCores.Add(new ProdutoCor
                                {
                                    CorId = produtoCor.CorId,
                                    ProdutoCorTamanhos = _mapper.Map<List<ProdutoCorTamanho>>(produtoCorTamanhosAdicionar)
                                });
                        }
                    }
                }
                else
                {
                    var produtoCorUnica = produtoAtualizado.ProdutoCores.FirstOrDefault(x => x.CorId == corPadraoSistema.Id);
                    if (produtoCorUnica != null)
                    {
                        var produtoCorTamanhoUnico = produtoCorUnica.ProdutoCorTamanhos.FirstOrDefault(x => x.TamanhoId == tamanhoPadraoSistema.Id);
                        if (produtoCorTamanhoUnico != null)
                        {
                            var produtoCorUnicaViewModel = produtoViewModel.ProdutoCores.FirstOrDefault();
                            if (produtoCorUnicaViewModel != null)
                            {
                                var produtoCorTamanhoUnicoViewModel = produtoCorUnicaViewModel.ProdutoCorTamanhos.FirstOrDefault();
                                if (produtoCorTamanhoUnicoViewModel != null)
                                {
                                    //Validacao para nao inserir produto com codigo externo ou gtin clonado
                                    if (await VerificarCodExternoGTINEAN(produtoCorTamanhoUnico.Id, produtoCorTamanhoUnicoViewModel.CodigoExterno, produtoCorTamanhoUnicoViewModel.CodigoGTINEAN))
                                        return;

                                    produtoCorTamanhoUnico.Altura = produtoViewModel.OutrasInformacoesAltura;
                                    produtoCorTamanhoUnico.Largura = produtoViewModel.OutrasInformacoesLargura;
                                    produtoCorTamanhoUnico.Profundidade = produtoViewModel.OutrasInformacoesProfundidade;
                                    produtoCorTamanhoUnico.PesoLiquido = produtoViewModel.OutrasInformacoesPesoLiquido;
                                    produtoCorTamanhoUnico.PesoBruto = produtoViewModel.OutrasInformacoesPesoBruto;

                                    produtoCorTamanhoUnico.EstoqueMinimo = produtoCorTamanhoUnicoViewModel.EstoqueMinimo;

                                    produtoCorTamanhoUnico.CodigoGTINEAN = produtoCorTamanhoUnicoViewModel.CodigoGTINEAN;
                                    produtoCorTamanhoUnico.CodigoExterno = produtoCorTamanhoUnicoViewModel.CodigoExterno;

                                    produtoCorTamanhoUnico.DataHoraUltimaAlteracao = DateTime.UtcNow;
                                }
                            }
                        }
                    }
                }
            }
            #endregion

            #region [ Kit itens ]
            List<ProdutoCorTamanhoKitViewModel> produtoCorTamanhoKitItensAdicionar = new();
            List<ProdutoCorTamanhoKit> produtoCorTamanhoKitItensDeletar = new();
            if (produtoAtualizado.TipoProduto == TipoProduto.PRODUTO_KIT)
            {
                var produtoCorUnica = produtoAtualizado.ProdutoCores.FirstOrDefault(p => p.CorId.Equals(corPadraoSistema.Id));
                var produtoCorUnicaViewModel = produtoViewModel.ProdutoCores.FirstOrDefault();

                if (produtoCorUnica != null && produtoCorUnicaViewModel != null)
                {
                    var produtoCorTamanhoUnico = produtoCorUnica.ProdutoCorTamanhos.FirstOrDefault(p => p.TamanhoId.Equals(tamanhoPadraoSistema.Id));
                    var produtoCorTamanhoUnicoViewModel = produtoCorUnicaViewModel.ProdutoCorTamanhos.FirstOrDefault();

                    if (produtoCorTamanhoUnico != null && produtoCorTamanhoUnicoViewModel != null)
                    {
                        var produtoCorTamanhoKitItens = await _produtoCorTamanhoKitRepository.ObterParaAlteracao(produtoCorTamanhoUnico.Id);

                        foreach (var produtoCorTamanhoKitItem in produtoCorTamanhoKitItens)
                        {
                            var produtoCorTamanhoKitItemViewModel = produtoCorTamanhoUnicoViewModel.ProdutoCorTamanhoKitItens.FirstOrDefault(p => p.ProdutoCorTamanhoItemId.Equals(produtoCorTamanhoKitItem.ProdutoCorTamanhoItemId));
                            if (produtoCorTamanhoKitItemViewModel != null)
                            {
                                produtoCorTamanhoKitItem.Quantidade = produtoCorTamanhoKitItemViewModel.Quantidade;
                                produtoCorTamanhoKitItem.ProdutoCorTamanhoKitPrecoLojas.First(x => x.LojaId.Equals(_aspNetUserInfo.LojaId.Value)).Valor = produtoCorTamanhoKitItemViewModel.Valor;
                            }
                        }

                        await _produtoCorTamanhoKitRepository.SaveChanges();

                        produtoCorTamanhoKitItensAdicionar = produtoCorTamanhoUnicoViewModel.ProdutoCorTamanhoKitItens
                                 .Where(x => produtoCorTamanhoKitItens.All(y => y.ProdutoCorTamanhoItemId != x.ProdutoCorTamanhoItemId))
                                 .ToList();

                        produtoCorTamanhoKitItensDeletar = produtoCorTamanhoKitItens
                                           .Where(x => produtoCorTamanhoUnicoViewModel.ProdutoCorTamanhoKitItens.All(y => y.ProdutoCorTamanhoItemId != x.ProdutoCorTamanhoItemId))
                                           .ToList();

                        var lojas = await _lojaRepository.ListarTodasLojas();

                        foreach (var produtoCorTamanhoKitItemAdicionar in produtoCorTamanhoKitItensAdicionar)
                        {
                            await _produtoCorTamanhoKitRepository.Insert(new ProdutoCorTamanhoKit()
                            {
                                Quantidade = produtoCorTamanhoKitItemAdicionar.Quantidade,
                                ProdutoCorTamanhoKitPrecoLojas = lojas.Select(l => new ProdutoCorTamanhoKitPrecoLoja
                                {
                                    LojaId = l.Id,
                                    Valor = produtoCorTamanhoKitItemAdicionar.Valor
                                }).ToList(),
                                ProdutoCorTamanhoItemId = produtoCorTamanhoKitItemAdicionar.ProdutoCorTamanhoItemId,
                                ProdutoCorTamanhoPrincipalId = produtoCorTamanhoUnico.Id
                            });
                        }

                        foreach (var produtoCorTamanhoKitItemDeletar in produtoCorTamanhoKitItensDeletar)
                        {
                            await _produtoCorTamanhoKitRepository.Delete(produtoCorTamanhoKitItemDeletar.Id);
                        }
                    }
                }
            }
            #endregion

            #region [ Tags ]

            if (produtoViewModel.Tags != null)
            {
                var tagsAdicionar = produtoViewModel.Tags
                                 .Where(tagId => produtoAtualizado.TagProdutos.All(tagProduto => tagProduto.TagId != tagId))
                                 .ToList();

                var tagsDeletar = produtoAtualizado.TagProdutos
                                   .Where(tagProduto => produtoViewModel.Tags.All(tagId => tagId != tagProduto.TagId))
                                   .ToList();

                foreach (var tagId in tagsAdicionar)
                    produtoAtualizado.TagProdutos.Add(new TagProduto() { TagId = tagId });

                foreach (var tag in tagsDeletar)
                    produtoAtualizado.TagProdutos.Remove(tag);
            }

            #endregion

            #region [ Campos personalizados ]

            if (produtoViewModel.CamposPersonalizados == null || !produtoViewModel.CamposPersonalizados.Any())
                produtoAtualizado.CampoPersonalizadoProduto = new List<CampoPersonalizadoProduto>();
            else
            {
                var camposPersonalizadosAdicionar = new List<CampoPersonalizadoValorViewModel>();

                if (produtoViewModel.CamposPersonalizados != null)
                {
                    camposPersonalizadosAdicionar = produtoViewModel.CamposPersonalizados
                        .Where(x => produtoAtualizado.CampoPersonalizadoProduto.All(y => y.CampoPersonalizadoId != x.CampoPersonalizadoId && !string.IsNullOrEmpty(x.Valor)))
                        .ToList();
                }

                var camposPersonalizadosDeletar = produtoAtualizado.CampoPersonalizadoProduto
                                   .Where(x => produtoViewModel.CamposPersonalizados.All(y => y.CampoPersonalizadoId != x.CampoPersonalizadoId || string.IsNullOrEmpty(y.Valor)))
                                   .ToList();

                foreach (var campoPersonalizado in camposPersonalizadosAdicionar)
                {
                    produtoAtualizado.CampoPersonalizadoProduto.Add(new CampoPersonalizadoProduto()
                    {
                        CampoPersonalizadoId = campoPersonalizado.CampoPersonalizadoId,
                        Valor = campoPersonalizado.Valor
                    });
                }

                foreach (var campoPersonalizado in camposPersonalizadosDeletar)
                    produtoAtualizado.CampoPersonalizadoProduto.Remove(campoPersonalizado);

                if (produtoAtualizado.CampoPersonalizadoProduto != null)
                {
                    foreach (var campoPersonalizadoProduto in produtoAtualizado.CampoPersonalizadoProduto)
                    {
                        var campoPersonalizadoViewModel = produtoViewModel.CamposPersonalizados
                            .FirstOrDefault(x =>
                                campoPersonalizadoProduto.CampoPersonalizadoId.Equals(x.CampoPersonalizadoId) &&
                                !campoPersonalizadoProduto.Valor.Equals(x.Valor));

                        if (campoPersonalizadoViewModel != null)
                            campoPersonalizadoProduto.Valor = campoPersonalizadoViewModel.Valor;
                    }
                }
            }

            #endregion

            #region [ Regras fiscais exceções ]

            if (produtoViewModel.ProdutoRegraFiscalExcecoes != null)
            {
                var regrasFiscaisExcecoesAdicionar = produtoViewModel.ProdutoRegraFiscalExcecoes
                                 .Where(x => produtoAtualizado.ProdutoRegraFiscalExcecoes.All(y => y.EstadoOrigemId != x.EstadoOrigemId || y.EstadoDestinoId != x.EstadoDestinoId))
                                 .ToList();

                var regrasFiscaisExcecoesDeletar = produtoAtualizado.ProdutoRegraFiscalExcecoes
                                   .Where(x => produtoViewModel.ProdutoRegraFiscalExcecoes.All(y => y.EstadoOrigemId != x.EstadoOrigemId || y.EstadoDestinoId != x.EstadoDestinoId))
                                   .ToList();

                foreach (var regraFiscalExcecao in regrasFiscaisExcecoesAdicionar)
                    produtoAtualizado.ProdutoRegraFiscalExcecoes.Add(_mapper.Map<ProdutoRegraFiscalExcecao>(regraFiscalExcecao));

                foreach (var regraFiscalExcecao in regrasFiscaisExcecoesDeletar)
                    produtoAtualizado.ProdutoRegraFiscalExcecoes.Remove(regraFiscalExcecao);

                if (produtoAtualizado.ProdutoRegraFiscalExcecoes != null)
                {
                    foreach (var produtoRegraFiscalExcecao in produtoAtualizado.ProdutoRegraFiscalExcecoes)
                    {
                        var regraFiscalExcecaoAlteracao = produtoViewModel.ProdutoRegraFiscalExcecoes.FirstOrDefault(x => x.EstadoOrigemId == produtoRegraFiscalExcecao.EstadoOrigemId && x.EstadoDestinoId == produtoRegraFiscalExcecao.EstadoDestinoId);

                        if (regraFiscalExcecaoAlteracao != null)
                        {
                            produtoRegraFiscalExcecao.CodigoBeneficioFiscal = regraFiscalExcecaoAlteracao.CodigoBeneficioFiscal;
                            produtoRegraFiscalExcecao.AliquotaIcms = regraFiscalExcecaoAlteracao.AliquotaIcms;
                            produtoRegraFiscalExcecao.PorcentagemFCP = regraFiscalExcecaoAlteracao.PorcentagemFCP;
                            produtoRegraFiscalExcecao.ReducaoBaseCalculo = regraFiscalExcecaoAlteracao.ReducaoBaseCalculo;
                        }
                    }
                }
            }

            #endregion

            produtoAtualizado.Nome = produtoViewModel.Nome;
            produtoAtualizado.NomeAbreviado = produtoViewModel.NomeAbreviado;
            produtoAtualizado.Referencia = produtoViewModel.Referencia;
            produtoAtualizado.CategoriaProdutoId = produtoViewModel.CategoriaProdutoId;
            produtoAtualizado.MarcaId = produtoViewModel.MarcaId;
            produtoAtualizado.UnidadeMedidaId = produtoViewModel.UnidadeMedidaId;
            produtoAtualizado.TipoProdutoFiscal = produtoViewModel.TipoProdutoFiscal;
            produtoAtualizado.CstOrigem = produtoViewModel.CstOrigem;
            produtoAtualizado.CodigoNcm = produtoViewModel.CodigoNcm;
            produtoAtualizado.CodigoCest = produtoViewModel.CodigoCest;
            produtoAtualizado.RegraFiscalId = produtoViewModel.RegraFiscalId;
            produtoAtualizado.IcmsAliquota = produtoViewModel.IcmsAliquota;
            produtoAtualizado.IcmsReducaoBaseCalculo = produtoViewModel.IcmsReducaoBaseCalculo;
            produtoAtualizado.CodigoBeneficioFiscal = produtoViewModel.CodigoBeneficioFiscal;
            produtoAtualizado.FcpAliquota = produtoViewModel.FcpAliquota;
            produtoAtualizado.IcmsStRetidoBaseCalculo = produtoViewModel.IcmsStRetidoBaseCalculo;
            produtoAtualizado.IcmsStRetidoValor = produtoViewModel.IcmsStRetidoValor;
            produtoAtualizado.FcpStRetidoBaseCalculo = produtoViewModel.FcpStRetidoBaseCalculo;
            produtoAtualizado.FcpStRetidoValor = produtoViewModel.FcpStRetidoValor;
            produtoAtualizado.PisAliquota = produtoViewModel.PisAliquota;
            produtoAtualizado.CofinsAliquota = produtoViewModel.CofinsAliquota;
            produtoAtualizado.UnidadeTributavelId = produtoViewModel.UnidadeTributavelId;
            produtoAtualizado.FatorConversao = produtoViewModel.FatorConversao;
            produtoAtualizado.QtdeConversao = produtoViewModel.QtdeConversao;
            produtoAtualizado.IndicadorEscalaRelevante = produtoViewModel.IndicadorEscalaRelevante;
            produtoAtualizado.CNPJFabricante = produtoViewModel.CNPJFabricante;
            produtoAtualizado.CodigoAnp = produtoViewModel.CodigoAnp;
            produtoAtualizado.CODIF = produtoViewModel.CODIF;
            produtoAtualizado.PercentualGLP = produtoViewModel.PercentualGLP;
            produtoAtualizado.PercentualGasNacional = produtoViewModel.PercentualGasNacional;
            produtoAtualizado.PercentualGasImportado = produtoViewModel.PercentualGasImportado;
            produtoAtualizado.ValorPartidaGLP = produtoViewModel.ValorPartidaGLP;
            produtoAtualizado.AliquotaAdREmICMSRetido = produtoViewModel.AliquotaAdREmICMSRetido;
            produtoAtualizado.QuantidadeBCMonoRetido = produtoViewModel.QuantidadeBCMonoRetido;
            produtoAtualizado.ControlaEstoque = produtoViewModel.ControlaEstoque;
            produtoAtualizado.PermiteAlteraValorNaVenda = produtoViewModel.PermiteAlteraValorNaVenda;
            produtoAtualizado.SolicitarInformacaoComplementarNoPdv = produtoViewModel.SolicitarInformacaoComplementarNoPdv;
            produtoAtualizado.UtilizarBalanca = produtoViewModel.UtilizarBalanca;
            produtoAtualizado.ExportarBalanca = produtoViewModel.ExportarBalanca;

            var valorAdicionarKit = produtoCorTamanhoKitItensAdicionar.Sum(x => x.Valor);

            foreach (var produtoPrecoViewModel in produtoViewModel.ProdutoPrecoLojas)
            {
                var produtoPreco = produtoAtualizado.ProdutoPrecoLojas.FirstOrDefault(pcl => pcl.LojaId.Equals(produtoPrecoViewModel.LojaId));

                if (produtoPreco == null)
                {
                    produtoAtualizado.ProdutoPrecoLojas.Add(new ProdutoPrecoLoja
                    {
                        PrecoCusto = produtoPrecoViewModel.PrecoCusto,
                        PrecoCompra = produtoPrecoViewModel.PrecoCompra,
                        Markup = produtoPrecoViewModel.Markup,
                        PrecoVenda = produtoPrecoViewModel.PrecoVenda,
                        LojaId = produtoPrecoViewModel.LojaId
                    });
                }
                else
                {
                    if (produtoAtualizado.TipoProduto.Equals(TipoProduto.PRODUTO_KIT) && produtoPrecoViewModel.LojaId != _aspNetUserInfo.LojaId.Value)
                    {
                        var valorRemoverKit = produtoCorTamanhoKitItensDeletar.Sum(p => p.ProdutoCorTamanhoKitPrecoLojas.Where(c => c.LojaId.Equals(produtoPreco.LojaId)).Sum(v => v.Valor));

                        produtoPrecoViewModel.PrecoVenda = produtoPrecoViewModel.PrecoVenda + valorAdicionarKit - valorRemoverKit;
                    }

                    produtoPreco.PrecoCusto = produtoPrecoViewModel.PrecoCusto;
                    produtoPreco.PrecoCompra = produtoPrecoViewModel.PrecoCompra;
                    produtoPreco.Markup = produtoPrecoViewModel.Markup;
                    produtoPreco.PrecoVenda = produtoPrecoViewModel.PrecoVenda;
                }
            }

            produtoAtualizado.DataHoraUltimaAlteracao = DateTime.UtcNow;

            _produtoRepository.AdicionarEvento(new ProdutoAlteradoEvent(produtoAtualizado.Id, produtoAtualizado.Ativo && !produtoViewModel.Ativo));
            _produtoRepository.AdicionarEvento(produtoPrecoCategoriaAlteradoEvent);

            MudarAtivoProduto(produtoAtualizado, produtoViewModel.Ativo);

            if (await _produtoRepository.SaveChanges() > 0)
                await _graficosProdutoSTi3DashboardService.AtualizarNomeProduto(produtoAtualizado.Id, produtoAtualizado.Nome);

            if (produtoViewModel.TipoProduto != TipoProduto.PRODUTO_KIT)
            {
                var produtoOperacaoViewModel = new ProdutoViewModel
                {
                    ProdutoCores = produtoOperacao.ProdutoCores.Where(x => x.ProdutoCorTamanhos.Any(x => x.ProdutoCorTamanhoEstoques.Any(x => x.EstoqueAtual > 0)))
                   .Select(x => new ProdutoCorViewModel
                   {
                       ProdutoCorTamanhos = x.ProdutoCorTamanhos.Select(y => new ProdutoCorTamanhoViewModel
                       {
                           Id = produtoAtualizado.ProdutoCores.Where(pc => pc.CorId.Equals(x.CorId)
                                                              && pc.ProdutoCorTamanhos.Any(pct => pct.TamanhoId.Equals(y.TamanhoId)))
                                                    .FirstOrDefault().ProdutoCorTamanhos.FirstOrDefault(pct => pct.TamanhoId.Equals(y.TamanhoId)).Id,
                           ProdutoCorTamanhoEstoques = y.ProdutoCorTamanhoEstoques.Select(e => new ProdutoCorTamanhoEstoqueViewModel { EstoqueAtual = e.EstoqueAtual }).ToArray()
                       }).ToArray()
                   }).ToArray()
                };

                if (produtoOperacaoViewModel.ProdutoCores.Count() > 0)
                    await GerarOperacaoDeEntradaEstoque(produtoOperacaoViewModel);
            }

            await _logAuditoriaService.Inserir(logAuditoria);

            _databaseTransaction.Commit();

            #region Trigger

            await _zendarTriggerService.ExecuteGuid(produtoAtualizado.Id,
                                                TabelaTrigger.PRODUTO,
                                                OperacaoTrigger.ALTERAR);

            #endregion

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produtoAtualizado.Id);
        }

        private bool ValidarPalavrasReservadas(ProdutoViewModel produto)
        {
            var palavrasReservadas = new string[] { "0X", "0x" };

            List<string> campos = new();

            if (!string.IsNullOrEmpty(produto.Referencia) && palavrasReservadas.Any(x => produto.Referencia.StartsWith(x)))
                campos.Add("referência");

            if (produto.ProdutoCores != null &&
                produto.ProdutoCores
                            .Where(pc => pc != null && pc.ProdutoCorTamanhos != null)
                            .Any(pc => pc.ProdutoCorTamanhos.Where(pct => pct != null && !string.IsNullOrEmpty(pct.CodigoExterno)).Any(pct => palavrasReservadas.Any(x => pct.CodigoExterno.StartsWith(x)))))
                campos.Add("código externo");

            if (campos.Any())
            {
                var mensagem = string.Empty;

                if (campos.ToArray().Length == 1)
                    mensagem = "O campo {0} possui uma ou mais palavras reservadas em sua composição.";
                else
                    mensagem = "Os campos {0} possuem uma ou mais palavras reservadas em sua composição.";

                NotificarAviso(string.Format(mensagem, string.Join(", ", campos)));
                return false;
            }

            return true;
        }

        public async Task ExcluirVariacoesTamanho(Guid produtoId, Guid tamanhoId)
        {
            var produto = await _produtoRepository
                .FirstOrDefaultAsNoTracking(p => p.Id.Equals(produtoId), p =>
               new Produto
               {
                   Id = p.Id,
                   Nome = p.Nome,
                   Referencia = p.Referencia,
                   ProdutoCores = p.ProdutoCores
                   .Select(y => new ProdutoCor()
                   {
                       Id = y.Id,
                       ProdutoCorTamanhos = y.ProdutoCorTamanhos
                       .Select(z => new ProdutoCorTamanho()
                       {
                           Id = z.Id,
                           TamanhoId = z.TamanhoId,
                           Tamanho = new Tamanho { PadraoSistema = z.Tamanho.PadraoSistema }
                       })
                       .ToList(),
                       Cor = new Cor()
                       {
                           Descricao = y.Cor.Descricao,
                           PadraoSistema = y.Cor.PadraoSistema
                       },
                   })
                   .ToList(),
               });

            if (produto == null)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_NaoEncontrado);
                return;
            }

            var tamanho = await _tamanhoRepository.FirstOrDefaultAsNoTracking(t => t.Id.Equals(tamanhoId), t => new Tamanho()
            {
                Descricao = t.Descricao
            });

            if (tamanho == null)
            {
                NotificarAviso(ResourceMensagem.TamanhoService_NaoEncontrado);
                return;
            }

            Tamanho tamanhoPadraoSistema = await _tamanhoRepository.FirstOrDefaultAsNoTracking(tamanho => tamanho.PadraoSistema, tamanho => new Tamanho() { Id = tamanho.Id });

            _databaseTransaction.BeginTransaction();

            foreach (var produtoCor in produto.ProdutoCores)
            {
                foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                {
                    if (produtoCorTamanho.TamanhoId == tamanhoId)
                    {
                        #region Trigger

                        await _zendarTriggerService.ExecuteGuid(produtoCorTamanho.Id,
                                                            TabelaTrigger.VARIACAO,
                                                            OperacaoTrigger.REMOVER);

                        await _zendarTriggerService.ExecuteGuid(produtoId,
                                                            TabelaTrigger.PRODUTO,
                                                            OperacaoTrigger.ALTERAR);

                        #endregion

                        _produtoCorTamanhoRepository.AdicionarEvento(new ProdutoAlteradoEvent(produto.Id, false));

                        await _produtoCorTamanhoRepository.Delete(produtoCorTamanho.Id);
                    }
                }

                if (produtoCor.ProdutoCorTamanhos.Count == 1)
                {
                    if (produtoCor.Cor.PadraoSistema && !produtoCor.ProdutoCorTamanhos.Any(pct => !pct.Tamanho.PadraoSistema))
                    {
                        NotificarAviso(ResourceMensagem.ProdutoService_VariacaoCorUnicaTamanhoUnico);
                        return;
                    }

                    var produtoCorTamanhoUnico = new ProdutoCorTamanho()
                    {
                        ProdutoCorId = produtoCor.Id,
                        TamanhoId = tamanhoPadraoSistema.Id,
                        ProdutoCorTamanhoEstoques = new List<ProdutoCorTamanhoEstoque>(),
                        CodigoBarrasInterno = "5000000000005",
                        Ativo = true,
                    };

                    _produtoCorTamanhoRepository.AdicionarEvento(new ProdutoAlteradoEvent(produto.Id, false));
                    await _produtoCorTamanhoRepository.Insert(produtoCorTamanhoUnico);
                }
            }

            _databaseTransaction.Commit();

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produtoId);

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.PRODUTO, LogAuditoriaOperacao.REMOVER, $"Remoção das variações do produto \"{produto.Nome}\" que utilizam o tamanho \"{tamanho.Descricao}\""));
        }

        public async Task ExcluirVariacoesCor(Guid produtoId, Guid corId)
        {
            var produto = await _produtoRepository.FirstOrDefaultAsNoTracking(x => x.Id.Equals(produtoId), x =>
               new Produto
               {
                   Id = x.Id,
                   Nome = x.Nome,
                   Referencia = x.Referencia,
                   ProdutoCores = x.ProdutoCores
                   .Select(y => new ProdutoCor()
                   {
                       Id = y.Id,
                       CorId = y.CorId,
                       ProdutoCorTamanhos = y.ProdutoCorTamanhos
                       .Select(z => new ProdutoCorTamanho()
                       {
                           Id = z.Id,
                           Tamanho = new Tamanho()
                           {
                               Id = z.Tamanho.Id,
                               PadraoSistema = z.Tamanho.PadraoSistema
                           }
                       })
                       .ToList(),
                   })
                   .ToList(),
               });

            if (produto == null)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_NaoEncontrado);
                return;
            }

            var cor = await _corRepository.FirstOrDefaultAsNoTracking(t => t.Id.Equals(corId), t => new Cor()
            {
                Descricao = t.Descricao,
                PadraoSistema = t.PadraoSistema
            });

            if (cor == null)
            {
                NotificarAviso(ResourceMensagem.CorService_NaoEncontrada);
                return;
            }

            _databaseTransaction.BeginTransaction();

            List<Guid> tamanhosAdicionarCorUnica = new List<Guid>();

            foreach (var produtoCor in produto.ProdutoCores)
            {
                if (produtoCor.CorId == corId)
                {
                    if (produto.ProdutoCores.Count == 1)
                    {
                        if (cor.PadraoSistema && !produtoCor.ProdutoCorTamanhos.Any(x => !x.Tamanho.PadraoSistema))
                        {
                            NotificarAviso(ResourceMensagem.ProdutoService_VariacaoCorUnicaTamanhoUnico);
                            return;
                        }

                        foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                        {
                            if (!produtoCorTamanho.Tamanho.PadraoSistema && !tamanhosAdicionarCorUnica.Any(x => x.Equals(produtoCorTamanho.Tamanho.Id)))
                            {
                                tamanhosAdicionarCorUnica.Add(produtoCorTamanho.Tamanho.Id);
                            }
                        }
                    }

                    #region Trigger

                    foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                    {
                        if (!produtoCorTamanho.Tamanho.PadraoSistema)
                        {
                            await _zendarTriggerService.ExecuteGuid(produtoCorTamanho.Id,
                                                                TabelaTrigger.VARIACAO,
                                                                OperacaoTrigger.REMOVER);

                            await _zendarTriggerService.ExecuteGuid(produtoId,
                                                                TabelaTrigger.PRODUTO,
                                                                OperacaoTrigger.ALTERAR);
                        }
                    }

                    #endregion

                    _produtoCorRepository.AdicionarEvento(new ProdutoAlteradoEvent(produto.Id, false));
                    await _produtoCorRepository.Delete(produtoCor.Id);
                }
            }

            if (tamanhosAdicionarCorUnica.Count > 0)
            {
                Cor corPadraoSistema = await _corRepository.FirstOrDefaultAsNoTracking(cor => cor.PadraoSistema, cor => new Cor() { Id = cor.Id });

                ProdutoCor produtoCorUnica = new ProdutoCor()
                {
                    Ativo = true,
                    ProdutoId = produto.Id,
                    CorId = corPadraoSistema.Id,
                    ProdutoCorTamanhos = tamanhosAdicionarCorUnica
                    .Select(tamanho => new ProdutoCorTamanho()
                    {
                        TamanhoId = tamanho,
                        ProdutoCorTamanhoEstoques = new List<ProdutoCorTamanhoEstoque>(),
                        CodigoBarrasInterno = "5000000000005",
                        Ativo = true,
                    })
                    .ToList()
                };

                _produtoCorRepository.AdicionarEvento(new ProdutoAlteradoEvent(produto.Id, false));
                await _produtoCorRepository.Insert(produtoCorUnica);

                await _produtoCorRepository.SaveChanges();
            }

            _databaseTransaction.Commit();

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(produtoId);

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.PRODUTO, LogAuditoriaOperacao.REMOVER, $"Remoção das variações do produto \"{produto.Nome}\" que utilizam a cor \"{cor.Descricao}\""));
        }

        public async Task<ProdutoViewModel> Obter(Guid id)
        {
            var produto = await _produtoRepository.Obter(id);

            if (produto == null)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_NaoEncontrado);
                return null;
            }

            if (produto.TipoProduto == TipoProduto.PRODUTO_KIT)
            {
                produto.ProdutoCores.Add(await _produtoCorRepository.FirstOrDefaultAsNoTracking(p => p.Produto.Id == id, p => new ProdutoCor
                {
                    ProdutoCorTamanhos = p.ProdutoCorTamanhos.Select(pct => new ProdutoCorTamanho
                    {
                        Id = pct.Id
                    }).ToList()
                }));
            }

            var produtoViewModel = _mapper.Map<ProdutoViewModel>(produto);

            foreach (var produtoPrecoVm in produtoViewModel.ProdutoPrecoLojas)
            {
                var produtoPreco = produto.ProdutoPrecoLojas.FirstOrDefault(pcl => pcl.LojaId.Equals(produtoPrecoVm.LojaId));

                produtoPrecoVm.LojaRazao = produtoPreco.Loja.RazaoSocial;
            }

            var categoriaProdutoOpcaoSelect = new CategoriaProdutoOpcaoSelectViewModel()
            {
                Id = produto.CategoriaProdutoId,
                PrimeiroNivel = produto.CategoriaProduto.Nome,
            };

            if (produto.CategoriaProduto.CategoriaProdutoPai.Nome != null)
            {
                categoriaProdutoOpcaoSelect.SegundoNivel = categoriaProdutoOpcaoSelect.PrimeiroNivel;
                categoriaProdutoOpcaoSelect.PrimeiroNivel = produto.CategoriaProduto.CategoriaProdutoPai.Nome;

                if (produto.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Nome != null)
                {
                    categoriaProdutoOpcaoSelect.TerceiroNivel = categoriaProdutoOpcaoSelect.SegundoNivel;
                    categoriaProdutoOpcaoSelect.SegundoNivel = categoriaProdutoOpcaoSelect.PrimeiroNivel;
                    categoriaProdutoOpcaoSelect.PrimeiroNivel = produto.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.Nome;

                    if (produto.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Nome != null)
                    {
                        categoriaProdutoOpcaoSelect.QuartoNivel = categoriaProdutoOpcaoSelect.TerceiroNivel;
                        categoriaProdutoOpcaoSelect.TerceiroNivel = categoriaProdutoOpcaoSelect.SegundoNivel;
                        categoriaProdutoOpcaoSelect.SegundoNivel = categoriaProdutoOpcaoSelect.PrimeiroNivel;
                        categoriaProdutoOpcaoSelect.PrimeiroNivel = produto.CategoriaProduto.CategoriaProdutoPai.CategoriaProdutoPai.CategoriaProdutoPai.Nome;
                    }
                }
            }

            produtoViewModel.CategoriaProdutoOpcaoSelect = categoriaProdutoOpcaoSelect;

            var ncm = _ncmRepository.ObterPorCodigo(produto.CodigoNcm);
            if (ncm != null)
            {
                produtoViewModel.NcmLabel = $"{ncm.Codigo} - {ncm.Descricao}";
            }

            produtoViewModel.Foto = _storageService.ObterUrlArquivoAcessoTemporario(StorageContaArmazenamento.Imagens, produtoViewModel.Foto);

            produtoViewModel.Tags = produto.TagProdutos
                .Select(tagProduto => tagProduto.TagId)
                .ToArray();

            var camposPersonalizados = await _campoPersonalizadoRepository.ListarPorTipoCadastro(TipoCadastroCampoPersonalizado.PRODUTO);

            var camposPersonalizadosProduto = new List<CampoPersonalizadoValorViewModel>();

            foreach (var campoPersonalizado in camposPersonalizados)
            {
                var campoPersonalizadoProduto = produto.CampoPersonalizadoProduto.FirstOrDefault(x => x.CampoPersonalizadoId.Equals(campoPersonalizado.Id));

                camposPersonalizadosProduto.Add(new CampoPersonalizadoValorViewModel()
                {
                    CampoPersonalizadoId = campoPersonalizado.Id,
                    CampoPersonalizado = new CampoPersonalizadoViewModel()
                    {
                        Id = campoPersonalizado.Id,
                        Tipo = campoPersonalizado.Tipo,
                        Nome = campoPersonalizado.Nome,
                        CasaDecimal = campoPersonalizado.CasaDecimal,
                        DescricaoInstrucao = campoPersonalizado.DescricaoInstrucao,
                        PlaceHolder = campoPersonalizado.PlaceHolder,
                        Tamanho = campoPersonalizado.Tamanho,
                    },
                    Valor = campoPersonalizadoProduto != null ? campoPersonalizadoProduto.Valor : null
                });
            }

            produtoViewModel.CamposPersonalizados = camposPersonalizadosProduto.ToArray();

            if (produtoViewModel.TipoProduto == TipoProduto.PRODUTO_KIT)
            {
                var produtoCor = produtoViewModel.ProdutoCores.FirstOrDefault();

                if (produtoCor != null)
                {
                    var produtoCorTamanho = produtoCor.ProdutoCorTamanhos.FirstOrDefault();

                    if (produtoCorTamanho != null)
                    {
                        var produtoCorTamanhoKitItens = await _produtoCorTamanhoKitRepository
                            .FindAllSelectAsNoTracking(
                            p => p.ProdutoCorTamanhoPrincipalId.Equals(produtoCorTamanho.Id),
                            p => new ProdutoCorTamanhoKit()
                            {
                                ProdutoCorTamanhoItemId = p.ProdutoCorTamanhoItemId,
                                ProdutoCorTamanhoItem = new ProdutoCorTamanho()
                                {
                                    Tamanho = new Tamanho()
                                    {
                                        Descricao = p.ProdutoCorTamanhoItem.Tamanho.Descricao
                                    },
                                    ProdutoCor = new ProdutoCor()
                                    {
                                        Cor = new Cor()
                                        {
                                            Descricao = p.ProdutoCorTamanhoItem.ProdutoCor.Cor.Descricao
                                        },
                                        Produto = new Produto()
                                        {
                                            Nome = p.ProdutoCorTamanhoItem.ProdutoCor.Produto.Nome,
                                            ProdutoPrecoLojas = p.ProdutoCorTamanhoItem.ProdutoCor.Produto.ProdutoPrecoLojas.Where(pcl => pcl.LojaId.Equals(_aspNetUserInfo.LojaId.Value)).Select(pcl => new ProdutoPrecoLoja
                                            {
                                                PrecoCusto = pcl.PrecoCusto,
                                                PrecoVenda = pcl.PrecoVenda,
                                            }).ToList(),
                                        }
                                    }
                                },
                                Quantidade = p.Quantidade,
                                ProdutoCorTamanhoKitPrecoLojas = p.ProdutoCorTamanhoKitPrecoLojas.Where(x => x.LojaId.Equals(_aspNetUserInfo.LojaId.Value)).ToList(),
                            });

                        produtoCorTamanho.ProdutoCorTamanhoKitItens = produtoCorTamanhoKitItens
                            .Select(p => new ProdutoCorTamanhoKitViewModel()
                            {
                                ProdutoCorTamanhoItemId = p.ProdutoCorTamanhoItemId,
                                Quantidade = p.Quantidade,
                                Valor = p.ProdutoCorTamanhoKitPrecoLojas.Where(x => x.LojaId.Equals(_aspNetUserInfo.LojaId.Value))
                                                                                                 .Select(x => x.Valor)
                                                                                                 .First(),
                                PrecoCusto = p.ProdutoCorTamanhoItem.ProdutoCor.Produto.ProdutoPrecoLojas.First().PrecoCusto,
                                PrecoVendaOriginal = p.ProdutoCorTamanhoItem.ProdutoCor.Produto.ProdutoPrecoLojas.First().PrecoVenda,

                                ItemProdutoDescricao = p.ProdutoCorTamanhoItem.ProdutoCor.Produto.Nome,
                                ItemCorDescricao = p.ProdutoCorTamanhoItem.ProdutoCor.Cor.Descricao,
                                ItemTamanhoDescricao = p.ProdutoCorTamanhoItem.Tamanho.Descricao
                            })
                            .ToArray();
                    }
                }
            }

            return produtoViewModel;
        }

        public async Task<ProdutoViewModel> ObterVariacoesProduto(Guid id, bool saldo = true)
        {
            var produto = await _produtoRepository.ObterVariacoesProduto(id, _aspNetUserInfo.LojaId.Value);

            var produtoViewModel = _mapper.Map<ProdutoViewModel>(produto);

            if (saldo)
            {
                foreach (var produtoCor in produtoViewModel.ProdutoCores)
                {
                    foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                    {
                        produtoCorTamanho.EstoqueAtual = produtoCorTamanho.ProdutoCorTamanhoEstoques.Where(x => x.LocalEstoque.LojaId.Equals(_aspNetUserInfo.LojaId.Value)).Sum(x => x.EstoqueAtual);
                    }
                }
            }

            return produtoViewModel;
        }

        public async Task Excluir(Guid id)
        {
            var produto = await _produtoRepository.FirstOrDefaultAsNoTracking(x => x.Id.Equals(id), x =>
               new Produto
               {
                   Nome = x.Nome
               });

            if (produto == null)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_NaoEncontrado);
                return;
            }

            // verificar se o cadastro estava vinculado ao caixa movel
            if (await _integracaoCaixaMovelImportacaoCadastroService.ValidarSeCadastroEstaImportado(produto.Id, TipoCadastroImportacaoIntegracao.PRODUTO))
                _produtoRepository.AdicionarEvento(new ProdutoExcluidoEvent(produto.Id));

            await _produtoRepository.Delete(id);

            await _logAuditoriaService
                  .Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.PRODUTO, LogAuditoriaOperacao.REMOVER, $"Nome: {produto.Nome}"));

            #region Trigger

            await _zendarTriggerService.ExecuteGuid(id,
                                                TabelaTrigger.PRODUTO,
                                                OperacaoTrigger.REMOVER);

            #endregion
        }

        public async Task Inativar(Guid id)
        {
            var produtoAtualizado = await _produtoRepository.ObterParaInativar(id);

            if (produtoAtualizado == null)
            {
                NotificarAviso(ResourceMensagem.ProdutoService_NaoEncontrado);
                return;
            }

            MudarAtivoProduto(produtoAtualizado, false);

            _produtoRepository.AdicionarEvento(new ProdutoAlteradoEvent(produtoAtualizado.Id, true));
            await _produtoRepository.SaveChanges();

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.PRODUTO, LogAuditoriaOperacao.ALTERAR, $"Nome: {produtoAtualizado.Nome}, Ativo > Inativo"));

            await _popularPesquisaProdutoService.InserirProdutoParaPesquisa(id);

            #region Trigger

            await _zendarTriggerService.ExecuteGuid(produtoAtualizado.Id,
                                                TabelaTrigger.PRODUTO,
                                                OperacaoTrigger.ALTERAR);

            #endregion
        }

        private static void MudarAtivoProduto(Produto produtoAtualizado, bool valor)
        {
            foreach (var produtoCor in produtoAtualizado.ProdutoCores)
            {
                produtoCor.Ativo = valor;
                produtoCor.DataHoraUltimaAlteracao = DateTime.UtcNow;

                foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                {
                    produtoCorTamanho.Ativo = valor;
                    produtoCorTamanho.DataHoraUltimaAlteracao = DateTime.UtcNow;
                }
            }

            produtoAtualizado.Ativo = valor;
            produtoAtualizado.DataHoraUltimaAlteracao = DateTime.UtcNow;
        }

        public async Task<GridPaginadaRetorno<ProdutoPaginadoViewModel>> ListarPaginado(GridPaginadaConsulta gridPaginada, ProdutoFiltrosViewModel produtoFiltrosViewModel)
        {
            var produtosIds = Enumerable.Empty<Guid>();

            if (!string.IsNullOrWhiteSpace(produtoFiltrosViewModel.NomeReferencia))
            {
                produtosIds = await _pesquisaProdutoService.ListarTodosProdutoId(produtoFiltrosViewModel.NomeReferencia);

                if (!produtosIds.Any())
                    return new GridPaginadaRetorno<ProdutoPaginadoViewModel>();
            }

            if (produtoFiltrosViewModel.CategoriasProduto != null)
            {
                var listaIdCategorias = new List<Guid>();

                foreach (var categoriaId in produtoFiltrosViewModel.CategoriasProduto)
                {
                    listaIdCategorias.AddRange(_categoriaProdutoService.ObterCategoriasVinculadas(categoriaId).Result);
                }

                produtoFiltrosViewModel.CategoriasProduto = listaIdCategorias;
            }

            var produtos = _produtoRepository.ListarPaginado(gridPaginada, produtoFiltrosViewModel, produtosIds, _aspNetUserInfo.LojaId.Value);

            foreach (var produto in produtos.Registros)
            {
                var precoProduto = await _produtoRepository.ObterPrecoVendaCustoPorLoja(produto.Id, _aspNetUserInfo.LojaId.Value);

                produto.Estoque = _produtoCorTamanhoEstoqueRepository.ObterSaldoProduto(produto.Id, _aspNetUserInfo.LojaId.Value).Result;
                produto.PrecoVenda = precoProduto.precoVenda;
                produto.PrecoCusto = precoProduto.precoCusto;
            }

            return produtos;
        }

        public async Task<GridPaginadaRetorno<ProdutoDetalhadoViewModel>> ListarFiltrosDetalhado(GridPaginadaConsulta gridPaginada, ProdutoDetalhadoFiltrosViewModel produtoDetalhadoFiltrosViewModel)
        {
            var produtosPaginados = _produtoRepository.ListarFiltrosDetalhadoPaginado(gridPaginada, produtoDetalhadoFiltrosViewModel, _aspNetUserInfo.LojaId.Value);

            foreach (var produto in produtosPaginados.Registros)
            {
                var (precoVenda, precoCusto) = await _produtoRepository.ObterPrecoVendaCustoPorLoja(produto.Id, _aspNetUserInfo.LojaId.Value);

                produto.Estoque = _produtoCorTamanhoEstoqueRepository.ObterSaldoProduto(produto.Id, _aspNetUserInfo.LojaId.Value).Result;
                produto.PrecoVenda = precoVenda;
            }

            return produtosPaginados;
        }

        public async Task<ProdutoDetalhadoViewModel> ObterPorSku(string sku)
        {
            return await _produtoRepository.ObterPorSku(sku, _aspNetUserInfo.LojaId.Value);
        }

        public async Task<IEnumerable<ProdutoDetalhadoViewModel>> ObterPorCodigoIntegracaoOuReferencia(string pesquisa)
        {
            return await _produtoRepository.ObterPorCodigoIntegracaoOuReferencia(pesquisa, _aspNetUserInfo.LojaId.Value);
        }

        public async Task<IEnumerable<ProdutoDetalhadoViewModel>> ObterPorCodigoGtinEanOuExterno(string pesquisa)
        {
            return await _produtoRepository.ObterPorCodigoGtinEanOuExterno(pesquisa, _aspNetUserInfo.LojaId.Value);
        }

        public async Task<ProdutoDetalhadoViewModel> ObterPorCodigoBarrasInternoOuSequencia(string pesquisa)
        {
            return await _produtoRepository.ObterPorCodigoBarrasInternoOuSequencia(pesquisa, _aspNetUserInfo.LojaId.Value);
        }


        public async Task<ObterSaldoVariacoesProdutoViewModel> ObterSaldoVariacoes(Guid produtoId, Guid? corId)
        {
            var retorno = new ObterSaldoVariacoesProdutoViewModel()
            {
                SaldoVariacoes = _produtoCorTamanhoEstoqueRepository.ObterSaldoVariacoes(produtoId, corId),
                ItensConsignados = new()
            };

            // recuperar os itens consignados
            var itensConsignados = await _operacaoItemObterService.ObterItensConsignadosListarSaldo(produtoId);

            retorno.ItensConsignados = itensConsignados.Select(i => new ItensConsignadosSaldoVariacaoViewModel
            {
                Cliente = i.Operacao.ClienteFornecedor.Nome,
                Data = i.Operacao.DataEmissao.AddHours(_aspNetUserInfo.TimezoneOffset.Value),
                Telefone = i.Operacao.ClienteFornecedor.Telefone,
                Quantidade = i.Quantidade,
                Variacao = FormatarDescricaoVariacao(i.ProdutoCorTamanho)
            }).ToList();

            return retorno;
        }

        private string FormatarDescricaoVariacao(ProdutoCorTamanho pct)
        {
            var variacao = "";

            var exibirTamanho = pct.Tamanho != null && !pct.Tamanho.PadraoSistema;

            if (pct.ProdutoCor != null && pct.ProdutoCor.Cor != null && !pct.ProdutoCor.Cor.PadraoSistema)
            {
                variacao += $"{pct.ProdutoCor.Cor.Descricao}";

                if (exibirTamanho)
                    variacao += " | ";
            }

            if (exibirTamanho)
            {
                variacao += $"{pct.Tamanho.Descricao}";
            }

            return variacao;
        }

        public decimal ObterSaldoTotalProduto(Guid produtoId)
        {
            return _produtoCorTamanhoEstoqueRepository.ObterSaldoTotalProduto(produtoId, _aspNetUserInfo.LojaId.Value);
        }

        public async Task<List<ProdutoVariacaoCorViewModel>> ListarProdutoCores(string nomeSkuCodigoExternoBarrasGtinEan)
        {
            return await _produtoCorRepository.ObterProdutoCores(nomeSkuCodigoExternoBarrasGtinEan);
        }

        private async Task GerarOperacaoDeEntradaEstoque(ProdutoViewModel produtoViewModel)
        {
            var planoEnum = await _lojaService.ObterPlano(_aspNetUserInfo.LojaId.Value);
            if (planoEnum != ReferenciaServicoStargate.PLANO_START && planoEnum != ReferenciaServicoStargate.PLANO_BASIC)
            {
                bool gerarOperacao = true;
                var operacaoRetorno = new OperacaoRetornoCadastroViewModel();
                var localEstoque = await _localEstoqueRepository.FirstOrDefault(x => x.LojaId.Equals(_aspNetUserInfo.LojaId.Value) && x.PadraoSistema, x => new LocalEstoque { Id = x.Id });

                foreach (var produtoCor in produtoViewModel.ProdutoCores)
                {
                    foreach (var produtoCorTamanho in produtoCor.ProdutoCorTamanhos)
                    {

                        if (gerarOperacao)
                        {
                            operacaoRetorno = await _operacaoService.Cadastrar(new OperacaoInserirViewModel
                            {
                                IdentificacaoTipoOperacao = IdentificacaoTipoOperacao.AJUSTE_ESTOQUE_ENTRADA,
                                LocalEstoqueId = localEstoque.Id,
                                LojaId = _aspNetUserInfo.LojaId.Value,
                                Tela = LogAuditoriaTela.PRODUTO,
                                UsuarioId = Guid.Parse(_aspNetUserInfo.Id),
                            }, false);
                            gerarOperacao = false;
                        }

                        await _operacaoItemService.Cadastrar(new OperacaoItem
                        {
                            OperacaoId = operacaoRetorno.Id,
                            Quantidade = produtoCorTamanho.ProdutoCorTamanhoEstoques.First().EstoqueAtual,
                            ProdutoCorTamanhoId = produtoCorTamanho.Id,
                        });
                    }
                }
            }
        }

        private async Task<bool> VerificarCodExternoGTINEAN(Guid id, string codigoExterno, string codigoGTINEAN)
        {
            if (await _produtoCorTamanhoRepository.VerificarCodExterno(id, codigoExterno))
            {
                NotificarAviso(ResourceMensagem.ProdutoService_JaCadastradoCodExtInformado);
                return true;
            }

            if (await _produtoCorTamanhoRepository.VerificarCodGtinEan(id, codigoGTINEAN))
            {
                NotificarAviso(ResourceMensagem.ProdutoService_JaCadastradoCodGTINInformado);
                return true;
            }

            return false;
        }

        public async Task<byte[]> ObterImagemProduto(Guid produtoId)
        {
            var urlImagem = await _produtoRepository.ObterUrlFoto(produtoId);

            if (string.IsNullOrEmpty(urlImagem))
                return null;

            try
            {
                return _storageService.Download(StorageContaArmazenamento.Imagens, urlImagem).ToArray();
            }
            catch (Azure.RequestFailedException ex)
            {
                if (ex.ErrorCode == "BlobNotFound")
                {
                    NotificarAvisoRegistroNaoEncontrada("foto");
                    return null;
                }
                throw ex;
            }
        }

        public async Task<(decimal precoVenda, decimal precoCusto)> ObterPrecoVendaCustoProduto(Guid produtoId, Guid lojaId)
        {
            return await _produtoRepository.ObterPrecoVendaCustoPorLoja(produtoId, lojaId);
        }

        public async Task<List<IdNomeViewModel>> ListarSelect(
            string produtoNomeReferencia,
            TipoProduto? tipoProduto = null)
        {
            var listaProduto = await _produtoRepository.ListarSelect(produtoNomeReferencia, tipoProduto);

            return listaProduto.Select(p => new IdNomeViewModel
            {
                Id = p.Id,
                Nome = p.Nome
            }).ToList();
        }

        #region [Alteração em massa]
        public async Task<GridPaginadaRetorno<IdNomeViewModel>> ObterProdutosPorCampoEValorAtual(
            GridPaginadaConsulta gridPaginada,
            ObterProdutosPorCampoEValorAtualViewModel obterProdutosPorCampoEValorAtualViewModel)
        {
            try
            {
                if (!ExecutarValidacao(new ObterProdutosPorCampoEValorAtualViewModel.Validator(), obterProdutosPorCampoEValorAtualViewModel))
                {
                    return null;
                }

                return await _produtoRepository.ListarPaginadoEmMassa(gridPaginada, obterProdutosPorCampoEValorAtualViewModel.NomeCampo, obterProdutosPorCampoEValorAtualViewModel.Valor);
            }
            catch (InvalidCastException ex)
            {
                NotificarAviso(ex.Message);
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex.InnerException);
            }
        }

        public async Task<ProdutoAlteracaoEmMassaRetornoViewModel> AlterarTodosProdutosEmMassa(
            AlterarTodosProdutosEmMassaViewModel alterarTodosProdutosEmMassaViewModel)
        {
            int linhasAfetadas = 0;

            try
            {
                if (!ExecutarValidacao(new AlterarTodosProdutosEmMassaViewModel.Validator(), alterarTodosProdutosEmMassaViewModel))
                {
                    return new();
                }

                var propName = alterarTodosProdutosEmMassaViewModel.NomeCampo.ObterNomeColuna();

                _databaseTransaction.BeginTransaction();

                switch (propName)
                {
                    // ALTERAÇÃO NA TABELA: ProdutoCorTamanho
                    case nameof(ProdutoCorTamanho.EstoqueMinimo):
                        var estoqueMinimoNovo = (decimal)ProdutoAlteracaoEmMassaExtensions.ConvertValue(alterarTodosProdutosEmMassaViewModel.ValorNovo, typeof(decimal));

                        var produtoCorTamanho = await _produtoCorTamanhoRepository.FindAll();

                        foreach (var pct in produtoCorTamanho)
                        {
                            pct.EstoqueMinimo = estoqueMinimoNovo;
                        }

                        linhasAfetadas = await _produtoCorTamanhoRepository.SaveChanges();
                        break;

                    // ALTERAÇÃO NA TABELA: ProdutoGerenciadorImpressao
                    case nameof(ProdutoGerenciadorImpressao.GerenciadorImpressaoId):
                        var produtosGerenciadorImpressao = await _produtoGerenciadorImpressaoRepository.ObterPorLoja(_aspNetUserInfo.LojaId.Value);

                        if (alterarTodosProdutosEmMassaViewModel.ValorNovo is null)
                        {
                            _produtoGerenciadorImpressaoRepository.DesvincularTodos(produtosGerenciadorImpressao);
                        }
                        else
                        {
                            var gerenciadorImpressaoId = ConverterParaGuid(alterarTodosProdutosEmMassaViewModel.ValorNovo);
                            // atualizar os produtos que já possuem vinculo
                            _produtoGerenciadorImpressaoRepository.AtualizarProdutosGerenciadorImpressao(gerenciadorImpressaoId, produtosGerenciadorImpressao);
                            // criar o vinculo dos outros que ainda não possuem
                            var produtosSemVinculo = await _produtoRepository
                                                            .Where(p => !produtosGerenciadorImpressao
                                                                        .Select(pgi => pgi.ProdutoId)
                                                                        .Contains(p.Id))
                                                            .Select(p => p.Id)
                                                            .ToListAsync();
                            _produtoGerenciadorImpressaoRepository.VincularGerenciadorImpressaoAosProdutos(gerenciadorImpressaoId, produtosSemVinculo);
                        }

                        linhasAfetadas = await _produtoGerenciadorImpressaoRepository.SaveChanges();
                        break;

                    // ALTERAÇÃO NA TABELA: Produto
                    default:
                        var produtos = await _produtoRepository.FindAll();

                        linhasAfetadas = await AtualizarPropTabelaProdutos(alterarTodosProdutosEmMassaViewModel.ValorNovo, propName, produtos);
                        break;
                }

                _databaseTransaction.Commit();
            }
            catch (InvalidCastException ex)
            {
                _databaseTransaction.Rollback();
                NotificarAviso(ex.Message);
            }
            catch (Exception ex)
            {
                _databaseTransaction.Rollback();
                throw new Exception(ex.Message, ex.InnerException);
            }

            return new()
            {
                LinhasAfetadas = linhasAfetadas
            };
        }

        public async Task<ProdutoAlteracaoEmMassaRetornoViewModel> AlterarProdutosFiltradosEmMassa(
            AlterarProdutosFiltradosEmMassaViewModel alterarProdutosFiltradosEmMassaViewModel)
        {
            int linhasAfetadas = 0;

            try
            {
                if (!ExecutarValidacao(new AlterarProdutosFiltradosEmMassaViewModel.Validator(), alterarProdutosFiltradosEmMassaViewModel))
                {
                    return new();
                }

                var propName = alterarProdutosFiltradosEmMassaViewModel.NomeCampo.ObterNomeColuna();

                _databaseTransaction.BeginTransaction();

                switch (propName)
                {
                    // ALTERAÇÃO NA TABELA: ProdutoCorTamanho
                    case nameof(ProdutoCorTamanho.EstoqueMinimo):
                        var estoqueMinimoNovo = (decimal)ProdutoAlteracaoEmMassaExtensions.ConvertValue(alterarProdutosFiltradosEmMassaViewModel.ValorNovo, typeof(decimal));

                        var produtosCorTamanho = await _produtoCorTamanhoRepository.ObterProdutosCorTamanhoAlteracaoEmMassa(
                            selecionados: alterarProdutosFiltradosEmMassaViewModel.Selecionados,
                            produtosIds: alterarProdutosFiltradosEmMassaViewModel.ProdutosIds,
                            marcasIds: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.MarcasIds,
                            categoriasIds: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.CategoriasIds,
                            status: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.Status);

                        foreach (var pct in produtosCorTamanho)
                        {
                            pct.EstoqueMinimo = estoqueMinimoNovo;
                        }

                        linhasAfetadas = await _produtoCorTamanhoRepository.SaveChanges();
                        break;

                    // ALTERAÇÃO NA TABELA: ProdutoGerenciadorImpressao
                    case nameof(ProdutoGerenciadorImpressao.GerenciadorImpressaoId):
                        var produtosGerenciadorImpressao = await _produtoGerenciadorImpressaoRepository.ObterPorLoja(_aspNetUserInfo.LojaId.Value);

                        Guid[] produtosEncontrados = Array.Empty<Guid>();
                        if (!alterarProdutosFiltradosEmMassaViewModel.Selecionados) // buscar exceção de produtos para edição
                        {
                            produtosEncontrados = await _produtoRepository.ObterIdsProdutosFiltradosAlteracaoEmMassa(
                                marcasIds: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.MarcasIds,
                                categoriasIds: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.CategoriasIds,
                                status: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.Status);

                            // ignorar os produtos que foram selecionados
                            produtosEncontrados = produtosEncontrados.Except(alterarProdutosFiltradosEmMassaViewModel.ProdutosIds).ToArray();
                        }

                        // separação dos produtos que serão manipulados (selecionados para edição ou exceção)
                        var produtosGerenciadorImpressaoParaManipulacao = alterarProdutosFiltradosEmMassaViewModel.Selecionados
                            ? produtosGerenciadorImpressao.Where(pgi => alterarProdutosFiltradosEmMassaViewModel.ProdutosIds.Contains(pgi.ProdutoId)).ToList()
                            : produtosGerenciadorImpressao.Where(pgi => produtosEncontrados.Contains(pgi.ProdutoId)).ToList();

                        if (alterarProdutosFiltradosEmMassaViewModel.ValorNovo is null)
                        {
                            _produtoGerenciadorImpressaoRepository.DesvincularTodos(produtosGerenciadorImpressaoParaManipulacao);
                        }
                        else
                        {
                            var gerenciadorImpressaoId = ConverterParaGuid(alterarProdutosFiltradosEmMassaViewModel.ValorNovo);
                            // atualizar os produtos que já possuem vinculo
                            _produtoGerenciadorImpressaoRepository.AtualizarProdutosGerenciadorImpressao(gerenciadorImpressaoId, produtosGerenciadorImpressaoParaManipulacao);
                            // criar o vinculo dos outros que ainda não possuem
                            var produtosSemVinculo = produtosEncontrados.Except(produtosGerenciadorImpressaoParaManipulacao.Select(pgi => pgi.ProdutoId)).ToList();
                            _produtoGerenciadorImpressaoRepository.VincularGerenciadorImpressaoAosProdutos(gerenciadorImpressaoId, produtosSemVinculo);
                        }

                        linhasAfetadas = await _produtoGerenciadorImpressaoRepository.SaveChanges();
                        break;

                    // ALTERAÇÃO NA TABELA: Produto
                    default:
                        var produtos = await _produtoRepository.ObterProdutosFiltradosEmMassa(
                            selecionados: alterarProdutosFiltradosEmMassaViewModel.Selecionados,
                            produtosIds: alterarProdutosFiltradosEmMassaViewModel.ProdutosIds,
                            marcasIds: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.MarcasIds,
                            categoriasIds: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.CategoriasIds,
                            status: alterarProdutosFiltradosEmMassaViewModel.FiltrosAplicados.Status);

                        linhasAfetadas = await AtualizarPropTabelaProdutos(alterarProdutosFiltradosEmMassaViewModel.ValorNovo, propName, produtos);
                        break;
                }

                _databaseTransaction.Commit();
            }
            catch (InvalidCastException ex)
            {
                _databaseTransaction.Rollback();
                NotificarAviso(ex.Message);
            }
            catch (Exception ex)
            {
                _databaseTransaction.Rollback();
                throw new Exception(ex.Message, ex.InnerException);
            }

            return new()
            {
                LinhasAfetadas = linhasAfetadas
            };
        }

        public async Task<ProdutoAlteracaoEmMassaRetornoViewModel> SubstituirValorEmMassa(
            SubstituirValorProdutoEmMassaViewModel substituirValorProdutoEmMassaViewModel)
        {
            int linhasAfetadas = 0;

            try
            {
                if (!ExecutarValidacao(new SubstituirValorProdutoEmMassaViewModel.Validator(), substituirValorProdutoEmMassaViewModel))
                {
                    return new();
                }

                var propName = substituirValorProdutoEmMassaViewModel.NomeCampo.ObterNomeColuna();

                _databaseTransaction.BeginTransaction();

                switch (propName)
                {
                    // ALTERAÇÃO NA TABELA: ProdutoCorTamanho
                    case nameof(ProdutoCorTamanho.EstoqueMinimo):
                        var estoqueMinimoAtual = (decimal)ProdutoAlteracaoEmMassaExtensions.ConvertValue(substituirValorProdutoEmMassaViewModel.ValorAtual, typeof(decimal));
                        var estoqueMinimoNovo = (decimal)ProdutoAlteracaoEmMassaExtensions.ConvertValue(substituirValorProdutoEmMassaViewModel.ValorNovo, typeof(decimal));

                        var produtosCorTamanho = await _produtoCorTamanhoRepository.ObterProdutosCorTamanhoAlteracaoEmMassa(
                            selecionados: substituirValorProdutoEmMassaViewModel.Selecionados,
                            produtosIds: substituirValorProdutoEmMassaViewModel.ProdutosIds,
                            estoqueMinimoAtual: estoqueMinimoAtual);

                        foreach (var pct in produtosCorTamanho)
                        {
                            pct.EstoqueMinimo = estoqueMinimoNovo;
                        }

                        linhasAfetadas = await _produtoCorTamanhoRepository.SaveChanges();
                        break;

                    // ALTERAÇÃO NA TABELA: ProdutoGerenciadorImpressao
                    case nameof(ProdutoGerenciadorImpressao.GerenciadorImpressaoId):
                        if (substituirValorProdutoEmMassaViewModel.ValorAtual is null)
                        {
                            await ProcessarAdicao(substituirValorProdutoEmMassaViewModel);
                        }
                        else
                        {
                            await ProcessarAtualizacaoOuRemocao(substituirValorProdutoEmMassaViewModel);
                        }

                        linhasAfetadas = await _produtoGerenciadorImpressaoRepository.SaveChanges();
                        break;

                    // ALTERAÇÃO NA TABELA: Produto
                    default:
                        var produtos = await _produtoRepository.BuscarProdutosSubstituirValorEmMassa(
                            propName: propName,
                            valorAtual: substituirValorProdutoEmMassaViewModel.ValorAtual,
                            selecionados: substituirValorProdutoEmMassaViewModel.Selecionados,
                            produtosIds: substituirValorProdutoEmMassaViewModel.ProdutosIds);

                        linhasAfetadas = await AtualizarPropTabelaProdutos(substituirValorProdutoEmMassaViewModel.ValorNovo, propName, produtos);
                        break;
                }

                _databaseTransaction.Commit();
            }
            catch (InvalidCastException ex)
            {
                _databaseTransaction.Rollback();
                NotificarAviso(ex.Message);
            }
            catch (Exception ex)
            {
                _databaseTransaction.Rollback();
                throw new Exception(ex.Message, ex.InnerException);
            }

            return new()
            {
                LinhasAfetadas = linhasAfetadas
            };
        }

        #region [Alteração em massa - Métodos privados]
        private Guid ConverterParaGuid(object valor) => (Guid)ProdutoAlteracaoEmMassaExtensions.ConvertValue(valor, typeof(Guid));

        private async Task<int> AtualizarPropTabelaProdutos(object valorNovo, string propName, List<Produto> produtos)
        {
            var propertyInfo = typeof(Produto).GetProperty(propName);

            object parsedValue = ProdutoAlteracaoEmMassaExtensions.ConvertValue(valorNovo, propertyInfo.PropertyType);

            foreach (var produto in produtos)
            {
                var prop = produto.GetType().GetProperty(propName);

                var oldValue = prop.GetValue(produto);

				prop.SetValue(produto, parsedValue);

				if (propName == nameof(Produto.CategoriaProdutoId))
				{
					await _integracaoCaixaMovelProdutoService.AlterarProduto(produto.Id, produto.CategoriaProdutoId);
                    await _importacaoCadastroIntegracaoService.AlterarProduto(produto.Id, produto.CategoriaProdutoId, (Guid)oldValue);
				}
			}

            return await _produtoRepository.SaveChanges();
        }

        private async Task ProcessarAdicao(SubstituirValorProdutoEmMassaViewModel args)
        {
            if (args.ValorNovo is null) return; // Nada a fazer

            var gerenciadorImpressaoId = ConverterParaGuid(args.ValorNovo);

            List<Guid> produtosDisponiveisParaVincular = await ObterProdutosDisponiveisParaVincularAoGerenciadorImpressao();

            // Determinar os produtos a serem manipulados com base na seleção
            var produtosSolicitados = FiltrarProdutos(args, produtosDisponiveisParaVincular);

            _produtoGerenciadorImpressaoRepository.VincularGerenciadorImpressaoAosProdutos(gerenciadorImpressaoId, produtosSolicitados);
        }

        private async Task ProcessarAtualizacaoOuRemocao(SubstituirValorProdutoEmMassaViewModel args)
        {
            var gerenciadorImpressaoIdAtual = ConverterParaGuid(args.ValorAtual);
            var idsProdutosAtuais = await _produtoGerenciadorImpressaoRepository.ObterIdsPorGerenciadorImpressao(gerenciadorImpressaoIdAtual);
            var produtosParaManipular = FiltrarProdutos(args, idsProdutosAtuais);

            if (args.ValorNovo is null)
            {
                // Remover vínculos
                await _produtoGerenciadorImpressaoRepository.DesvincularTodos(produtosParaManipular);
            }
            else
            {
                // Atualizar o gerenciador de impressão
                var gerenciadorImpressaoIdNovo = ConverterParaGuid(args.ValorNovo);
                await _produtoGerenciadorImpressaoRepository.AtualizarProdutosGerenciadorImpressao(gerenciadorImpressaoIdNovo, produtosParaManipular);
            }
        }

        private async Task<List<Guid>> ObterProdutosDisponiveisParaVincularAoGerenciadorImpressao()
        {
            var todosProdutos = await _produtoRepository.FindAll();
            var produtosIds = todosProdutos.Select(p => p.Id).ToList();

            var produtosVinculados = await _produtoGerenciadorImpressaoRepository.ObterIdsPorLoja(_aspNetUserInfo.LojaId.Value);
            return produtosIds.Except(produtosVinculados).ToList();
        }

        private List<Guid> FiltrarProdutos(SubstituirValorProdutoEmMassaViewModel args, List<Guid> produtosBase)
            => args.Selecionados
            ? args.ProdutosIds.Intersect(produtosBase).ToList()
            : produtosBase.Except(args.ProdutosIds).ToList();
        #endregion

        #endregion

        private bool PossuiGtinEanRepetido(ProdutoViewModel produto)
        {
            var codigosGtinEan = produto.ProdutoCores
                                            .SelectMany(pc => pc.ProdutoCorTamanhos)
                                            .Select(pct => pct.CodigoGTINEAN);

            bool possuiGtinEanDuplicado = codigosGtinEan
                                            .Where(codigo => !string.IsNullOrWhiteSpace(codigo))
                                            .Any(codigo => codigosGtinEan.Count(c => c == codigo) > 1);

            if (!possuiGtinEanDuplicado) return false;

            NotificarAviso(ResourceMensagem.ProdutoService_CodGTINJaInformado);
            return true;
        }

        public void Dispose()
        {
            _produtoRepository?.Dispose();
            _produtoCorRepository?.Dispose();
            _produtoCorTamanhoRepository?.Dispose();
            _produtoCorTamanhoKitRepository?.Dispose();
            _produtoCorTamanhoEstoqueRepository.Dispose();
            _campoPersonalizadoRepository?.Dispose();
            _tagProdutoRepository?.Dispose();
            _corRepository?.Dispose();
            _tamanhoRepository?.Dispose();
            _databaseTransaction?.Dispose();
            _produtoCorTamanhoService?.Dispose();
            _operacaoItemObterService?.Dispose();
            _graficosProdutoSTi3DashboardService?.Dispose();
            _popularPesquisaProdutoService?.Dispose();
            _pesquisaProdutoService?.Dispose();
			_importacaoCadastroIntegracaoService?.Dispose();
        }

        public async Task<IEnumerable<ProdutoBalancaViewModel>> ListarProdutosImportadosBalanca(DateTime dataHoraLocal, Guid lojaId)
        {
            var produtosBalanca = await _produtoCorTamanhoRepository.ListarProdutosImportadosBalanca(dataHoraLocal, lojaId);

            TelaUsoPromocao[] telasPermitidas = 
            {
                TelaUsoPromocao.CONTROLE_MESA,
                TelaUsoPromocao.VENDAS_DELIVERY,
                TelaUsoPromocao.VENDAS_BALCAO
            };

            return produtosBalanca.Select(p =>
            {
                decimal precoPromocional = p.PromocaoItens
                        .Where(s => s.Promocao.TelasUsoPromocao
                            .Split(',')
                            .Select(Enum.Parse<TelaUsoPromocao>)
                            .Any(tela => telasPermitidas.Contains(tela)))
                        .Select(s => s.PrecoVenda)
                        .FirstOrDefault();

                decimal precoPadrao = p.ProdutoCor.Produto.ProdutoPrecoLojas
                        .Select(o => o.PrecoVenda)
                        .FirstOrDefault();

                return new ProdutoBalancaViewModel
                {
                    CodigoProduto = p.ProdutoCor.Produto.CodigoIntegracao,
                    Descricao = p.ProdutoCor.Produto.ObterNomeAbreviado(),
                    DiaValidade = p.ProdutoCor.Produto.DiasParaValidade,
                    PossuiComposicao = !string.IsNullOrEmpty(p.ProdutoCor.Produto.ComposicaoProduto),
                    UtilizarBalanca = p.ProdutoCor.Produto.UtilizarBalanca,
                    PrecoVenda = precoPromocional > 0 ? precoPromocional : precoPadrao
                };
            });
        }

        public async Task<IEnumerable<ProdutoComposicaoViewModel>> ListarComposicoesProdutos()
        {
            return await _produtoCorTamanhoRepository.ListarComposicoesProdutos();
        }
    }
}
