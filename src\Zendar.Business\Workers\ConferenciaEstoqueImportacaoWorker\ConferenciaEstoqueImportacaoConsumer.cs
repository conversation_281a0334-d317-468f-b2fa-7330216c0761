﻿using Azure.Messaging.ServiceBus;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Multiempresa.Shared.Enums;
using Multiempresa.Shared.Helpers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Zendar.Business.SignalR;
using Zendar.Business.ViewModels;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Emails;
using Zendar.QueueService;

namespace Zendar.Business.Workers.ConferenciaEstoqueWorker
{
    public class ConferenciaEstoqueImportacaoConsumer : IHostedService
    {
        private ServiceBusProcessor _processor;
        private readonly ServiceBusClient _client;

        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IHostingEnvironment _hostingEnvironment;

        public ConferenciaEstoqueImportacaoConsumer(IConfiguration configuration,
                                          IServiceProvider serviceProvider,
                                          IHttpClientFactory httpClientFactory,
                                          IHostingEnvironment hostingEnvironment)
        {
            _client = new ServiceBusClient(configuration["ServiceBusSettings:DefaultConnection"]);
            _configuration = configuration;
            _serviceProvider = serviceProvider;
            _httpClientFactory = httpClientFactory;
            _hostingEnvironment = hostingEnvironment;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            await RegisterQueue();
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }

        public async Task RegisterQueue()
        {
            try
            {
                _processor = _client.CreateProcessor(QueueNames.ImportacaoConferenciaEstoque, new ServiceBusProcessorOptions());
                _processor.ProcessMessageAsync += MessageHandler;
                _processor.ProcessErrorAsync += ErrorHandler;

                await _processor.StartProcessingAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                EnviarEmailErro(new LogErroInserirViewModel
                {
                    Erro = "Erro ao iniciar o processamento da fila.",
                    Dados = JsonConvert.SerializeObject(ex.InnerException)
                });
            }
        }

        private Task ErrorHandler(ProcessErrorEventArgs args)
        {
            EnviarEmailErro(new LogErroInserirViewModel
            {
                Erro = "ErrorHandler: Ocorreu um erro durante o processamente da mensagem.",
                Dados = JsonConvert.SerializeObject(args.Exception.InnerException)
            });
            return Task.CompletedTask;
        }

        private Task MessageHandler(ProcessMessageEventArgs args)
        {
            var mensagem = args.Message.Body.ToObjectFromJson<ConferenciaEstoqueImportacaoMessage>();
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var _conferenciaEstoqueImportacaoRepository = scope.ServiceProvider.GetService<IConferenciaEstoqueImportacaoRepository>();
                    _conferenciaEstoqueImportacaoRepository.SetConnectionString(ObterConexao(mensagem.CodigoContaEmpresa));

                    var _conferenciaEstoqueRepository = scope.ServiceProvider.GetService<IConferenciaEstoqueRepository>();
                    _conferenciaEstoqueRepository.SetConnectionString(ObterConexao(mensagem.CodigoContaEmpresa));

                    var _produtoCorTamanhoRepository = scope.ServiceProvider.GetService<IProdutoCorTamanhoRepository>();
                    _produtoCorTamanhoRepository.SetConnectionString(ObterConexao(mensagem.CodigoContaEmpresa));

                    var _conferenciaEstoqueItensRepository = scope.ServiceProvider.GetService<IConferenciaEstoqueItensRepository>();
                    _conferenciaEstoqueItensRepository.SetConnectionString(ObterConexao(mensagem.CodigoContaEmpresa));

                    var _hubContext = scope.ServiceProvider.GetService<IHubContext<NotificationHubService>>();


                    var conferenciaEstoqueImportacao = _conferenciaEstoqueImportacaoRepository.FirstOrDefault(x => x.ConferenciaEstoqueId == mensagem.ConferenciaEstoqueId).Result;

                    if (conferenciaEstoqueImportacao == null || conferenciaEstoqueImportacao.StatusImportacao == StatusImportacao.FALHA)
                    {
                        return Task.CompletedTask;
                    }
                    else if (conferenciaEstoqueImportacao.StatusImportacao == StatusImportacao.AGUARDANDO)
                    {
                        conferenciaEstoqueImportacao.StatusImportacao = StatusImportacao.EM_PROCESSAMENTO;

                        _conferenciaEstoqueImportacaoRepository.SaveChanges().Wait();
                    }

                    #region [Processamento]
                    var ItensAdicionados = new List<ConferenciaEstoqueItens>();

                    var conferencia = _conferenciaEstoqueRepository.ObterItensConferencia(mensagem.ConferenciaEstoqueId).Result;

                    var codigosDeBarras = mensagem.CodigosBarraInternoQuantidade.Select(c => c.CodigoBarrasInterno);
                    var codigosReduzidos = new List<int>();

                    foreach (var c in codigosDeBarras)
                    {
                        var (ehCodigoBarrasReduzido, sequencia) = CodigoBarrasReduzidoHelper.ValidarCodigoBarras(c);
                        if (ehCodigoBarrasReduzido)
                            codigosReduzidos.Add(sequencia);
                    }

                    var codigosBarrasCompletos = codigosDeBarras
                        .Where(c => !CodigoBarrasReduzidoHelper.ValidarCodigoBarras(c).isValid)
                        .ToList();

                    var produtoCorTamanhos = _produtoCorTamanhoRepository
                        .FindAllSelectAsNoTracking(
                            pct =>
                                (codigosBarrasCompletos.Contains(pct.CodigoBarrasInterno) ||
                                 codigosReduzidos.Contains(pct.SequenciaCodigoBarras)) &&
                                !pct.ProdutoCor.Produto.TipoProduto.Equals(TipoProduto.PRODUTO_KIT),
                            pct => new ProdutoCorTamanho
                            {
                                Id = pct.Id,
                                CodigoBarrasInterno = pct.CodigoBarrasInterno,
                                SequenciaCodigoBarras = pct.SequenciaCodigoBarras
                            })
                        .Result;

                    foreach (var produto in mensagem.CodigosBarraInternoQuantidade)
                    {
                        // 1. Tenta localizar diretamente por CodigoBarrasInterno
                        var produtoCorTamanho = produtoCorTamanhos.FirstOrDefault(x => x.CodigoBarrasInterno == produto.CodigoBarrasInterno);

                        // 2. Se não achou, tenta via código de barras reduzido (hexadecimal convertido)
                        if (produtoCorTamanho == null)
                        {
                            var (isValid, decimalValue) = CodigoBarrasReduzidoHelper.ValidarCodigoBarras(produto.CodigoBarrasInterno);

                            if (isValid)
                            {
                                produtoCorTamanho = produtoCorTamanhos
                                    .FirstOrDefault(x => x.SequenciaCodigoBarras == decimalValue);
                            }
                        }

                        if (produtoCorTamanho == null)
                            continue;

                        var conferenciaEstoqueItem = conferencia.ConferenciaEstoqueItens
                                                                .FirstOrDefault(x => x.ProdutoCorTamanhoId == produtoCorTamanho.Id);

                        if (conferenciaEstoqueItem == null)
                        {
                            ItensAdicionados.Add(new ConferenciaEstoqueItens
                            {
                                ConferenciaEstoqueId = mensagem.ConferenciaEstoqueId,
                                ProdutoCorTamanhoId = produtoCorTamanho.Id,
                                Quantidade = produto.Quantidade,
                                DataHoraUltimaAlteracao = DateTime.UtcNow
                            });
                        }
                        else
                        {
                            conferenciaEstoqueItem.DataHoraUltimaAlteracao = DateTime.UtcNow;
                            conferenciaEstoqueItem.Quantidade += produto.Quantidade;
                        }
                    }

                    _conferenciaEstoqueItensRepository.InsertRange(ItensAdicionados).Wait();

                    //para produtos editados
                    _conferenciaEstoqueItensRepository.SaveChanges().Wait();
                    #endregion

                    //Ao terminar de processar o ultima lote, verifica se tem algum item adicionado,  caso não, retorna falha, caso sim, retorna sucesso
                    if (conferenciaEstoqueImportacao.QuantidadeLote == mensagem.Lote)
                    {
                        if (conferencia.ConferenciaEstoqueItens.Count == 0)
                        {
                            conferenciaEstoqueImportacao.StatusImportacao = StatusImportacao.FALHA;

                            _conferenciaEstoqueImportacaoRepository.SaveChanges().Wait();

                            _hubContext.Clients.Group($"importacao-conferencia-estoque-{mensagem.UsuarioId}").SendAsync("Status", StatusImportacao.FALHA.ToString());

                            return Task.CompletedTask;
                        }

                        _hubContext.Clients.Group($"importacao-conferencia-estoque-{mensagem.UsuarioId}").SendAsync("Status", StatusImportacao.CONCLUIDA.ToString());

                        conferenciaEstoqueImportacao.StatusImportacao = StatusImportacao.CONCLUIDA;

                        _conferenciaEstoqueImportacaoRepository.SaveChanges().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var _conferenciaEstoqueImportacaoRepository = scope.ServiceProvider.GetService<IConferenciaEstoqueImportacaoRepository>();
                    _conferenciaEstoqueImportacaoRepository.SetConnectionString(ObterConexao(mensagem.CodigoContaEmpresa));
                    var _conferenciaEstoqueItensRepository = scope.ServiceProvider.GetService<IConferenciaEstoqueItensRepository>();
                    _conferenciaEstoqueItensRepository.SetConnectionString(ObterConexao(mensagem.CodigoContaEmpresa));
                    var _hubContext = scope.ServiceProvider.GetService<IHubContext<NotificationHubService>>();

                    var conferenciaEstoqueImportacao = _conferenciaEstoqueImportacaoRepository.FirstOrDefault(x => x.ConferenciaEstoqueId == mensagem.ConferenciaEstoqueId).Result;

                    if (conferenciaEstoqueImportacao.Tentativa == 3)
                    {
                        conferenciaEstoqueImportacao.StatusImportacao = StatusImportacao.FALHA;

                        _conferenciaEstoqueImportacaoRepository.SaveChanges().Wait();

                        var conferenciaItens = _conferenciaEstoqueItensRepository.ObterItensConferencia(mensagem.ConferenciaEstoqueId).Result;
                        _conferenciaEstoqueItensRepository.DeleteRange(conferenciaItens).Wait();

                        _hubContext.Clients.Group($"importacao-conferencia-estoque-{mensagem.UsuarioId}").SendAsync("Status", StatusImportacao.FALHA.ToString());
                    }
                    else
                    {
                        conferenciaEstoqueImportacao.Tentativa++;
                        _conferenciaEstoqueImportacaoRepository.SaveChanges().Wait();
                        args.AbandonMessageAsync(args.Message);
                    }
                }

                EnviarEmailErro(new LogErroInserirViewModel
                {
                    Erro = "MessageHandler: Ocorreu um erro durante o processamente da mensagem.",
                    Dados = JsonConvert.SerializeObject(ex)
                });
            }

            return Task.CompletedTask;
        }

        private void EnviarEmailErro(LogErroInserirViewModel logErroInserirViewModel)
        {
            var corpoEmail = string.Format(ResourceEmail.NotificadorService_LogErro,
                                            DateTime.UtcNow,
                                            "Queue Processor",
                                            "Server",
                                            "Importação conferência estoque",
                                            logErroInserirViewModel.Erro,
                                            logErroInserirViewModel.Dados);

            var emailEnvio = new EmailEnvio
            {
                Sistema = "Zendar",
                Assunto = "Erro inesperado!",
                Corpo = corpoEmail,
                Emails = _configuration.GetValue<string>("EmailSettings:EmailsLogErro").Split(',').ToList()
            };

            if (emailEnvio.Emails.Any())
            {
                try
                {
                    var httpClient = _httpClientFactory.CreateClient();

                    var stringContent = new StringContent(JsonConvert.SerializeObject(emailEnvio), System.Text.Encoding.UTF8, "application/json");

                    httpClient.PostAsync($"{ObterEnderecoWS()}/email", stringContent).Wait();
                }
                catch { }
            }
        }

        public string ObterConexao(string nomeBancoDados)
        {
            return string.Format(_configuration.GetConnectionString("DefaultConnection"), nomeBancoDados);
        }

        public string ObterEnderecoWS()
        {
            if (_hostingEnvironment.IsProduction())
                return "https://zendar-multiempresa-api.azurewebsites.net/api";

            else if (_hostingEnvironment.IsStaging())
                return "https://stargate-homolog-api.azurewebsites.net/api";

            else
                return "https://localhost:44331/api";
        }
    }
}
